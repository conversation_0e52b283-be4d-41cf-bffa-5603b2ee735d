# MQTT Testing Guide

This guide explains how to use the new MQTT testing features in the CEBS SCADA Android app.

## Overview

The MQTT testing screen provides comprehensive tools for testing and validating MQTT communication in your SCADA application. It includes:

- **Quick Test Tab**: Fast connection testing with popular public brokers
- **Broker Setup Tab**: Configure and manage custom MQTT brokers
- **Message Testing Tab**: Send/receive messages and manage subscriptions
- **Test Scenarios Tab**: Run automated test sequences
- **Diagnostics Tab**: Monitor connection status and performance

## Getting Started

1. **Launch the App**: Open the CEBS SCADA app
2. **Navigate to MQTT**: Tap the "MQTT" tab in the bottom navigation
3. **Start Testing**: Choose from quick tests or configure a custom broker

## Quick Test Tab

### Public Broker Testing
The Quick Test tab provides one-click connections to popular public MQTT brokers:

- **Eclipse Mosquitto Test Server** (`test.mosquitto.org:1883`)
- **Eclipse IoT Test Server** (`mqtt.eclipseprojects.io:1883`)
- **HiveMQ Public Broker** (`broker.hivemq.com:1883`)
- **Local Broker** (`localhost:1883`)

### Quick Actions
Once connected, you can:
- **Subscribe Test**: Subscribe to `test/topic`
- **Send Test**: Send a "Hello MQTT!" message
- **Disconnect**: Clean disconnect from the broker

## Broker Setup Tab

### Adding Custom Brokers
1. Tap "Add MQTT Broker"
2. Configure broker settings:
   - **Name**: Friendly name for the broker
   - **Address**: Broker hostname or IP address
   - **Port**: MQTT port (default: 1883)
   - **Username/Password**: Authentication credentials (optional)
   - **TLS**: Enable secure connection (optional)
   - **Client ID**: Custom client identifier (optional)

### Managing Brokers
- **Connect**: Establish connection to a configured broker
- **Remove**: Delete a broker configuration
- View broker details including protocol type and authentication status

## Message Testing Tab

### Sending Messages
1. **Topic**: Enter the MQTT topic (e.g., `sensors/temperature`)
2. **Message**: Enter your message content
3. **Send**: Publish the message to the broker

### Predefined Test Messages
Use the "Test Messages" dropdown to select from predefined message templates:
- **Simple Text**: Basic connectivity test
- **JSON Sensor Data**: Simulated sensor readings
- **Device Status**: Device health information
- **Control Command**: Relay control messages
- **Alarm Notification**: Critical alerts
- **Large Payload**: Performance testing
- **Special Characters**: Unicode and emoji support

### Subscription Management
- **Subscribe**: Enter topic patterns (supports wildcards `+` and `#`)
- **Quick Subscribe**: One-click subscription to common patterns
- **Bulk Subscribe**: Subscribe to multiple topics at once
- **Active Subscriptions**: View and manage current subscriptions

### Message History
- **Filtering**: Filter by topic, direction (incoming/outgoing), or search content
- **Real-time Updates**: Messages appear automatically as they arrive
- **Message Details**: View timestamp, QoS level, retained flag, and size
- **Export**: Save message history for analysis

## Test Scenarios Tab

### Automated Testing
Run predefined test sequences to validate MQTT functionality:

1. **Basic Connectivity Test**
   - Connect to broker
   - Subscribe to test topic
   - Publish and verify message delivery
   - Clean disconnect

2. **QoS Level Testing**
   - Test different Quality of Service levels (0, 1, 2)
   - Compare delivery behavior

3. **Wildcard Subscription Test**
   - Test single-level (`+`) and multi-level (`#`) wildcards
   - Verify pattern matching

4. **Retained Message Test**
   - Publish retained messages
   - Verify persistence across connections

5. **Load Testing**
   - Send burst of messages
   - Monitor performance and delivery rates

### Running Tests
1. Select a test scenario
2. Tap "Start Test" to begin automated execution
3. Monitor progress and results
4. Use "Reset" to restart or "Stop Test" to abort

## Diagnostics Tab

### Connection Monitoring
- **Real-time Status**: Current connection state with visual indicators
- **Broker Information**: Connected broker details and configuration
- **Connection Statistics**: Uptime, message counts, and performance metrics
- **Network Diagnostics**: Connection quality and latency information

### Troubleshooting
- **Connection Issues**: Check broker address, port, and credentials
- **Message Delivery**: Verify topic patterns and QoS settings
- **Performance**: Monitor message rates and connection stability

## Best Practices

### Testing Strategy
1. **Start Simple**: Use public brokers for initial testing
2. **Test Incrementally**: Begin with basic connectivity, then add complexity
3. **Use Scenarios**: Run automated tests to validate functionality
4. **Monitor Performance**: Check diagnostics for optimization opportunities

### Security Considerations
- **Use TLS**: Enable encryption for production brokers
- **Authentication**: Configure username/password for secure brokers
- **Topic Security**: Be mindful of topic patterns and access control
- **Public Brokers**: Only use for testing, never for production data

### Performance Tips
- **QoS Selection**: Choose appropriate QoS level for your use case
- **Message Size**: Keep messages reasonably sized for better performance
- **Connection Pooling**: Reuse connections when possible
- **Subscription Management**: Unsubscribe from unused topics

## Integration with SCADA System

The MQTT testing screen integrates seamlessly with the existing SCADA communication system:

- **Shared Configuration**: Broker settings are shared across the app
- **Message History**: All MQTT messages are logged in the central message store
- **Protocol Switching**: Easy switching between Bluetooth and MQTT protocols
- **Unified Interface**: Consistent UI patterns across communication methods

## Troubleshooting

### Common Issues

**Connection Failed**
- Verify broker address and port
- Check network connectivity
- Confirm authentication credentials
- Try a different broker or public test server

**Messages Not Received**
- Verify subscription topic patterns
- Check QoS level compatibility
- Confirm broker supports the feature
- Review message filtering settings

**Poor Performance**
- Reduce message frequency
- Optimize message size
- Check network quality
- Consider QoS level adjustment

### Getting Help
- Use the Diagnostics tab for connection details
- Check message history for delivery confirmation
- Try automated test scenarios for systematic validation
- Review broker logs if available

## Advanced Features

### Custom Test Scenarios
The test scenario system can be extended with custom test sequences for specific use cases.

### Message Export
Export message history for offline analysis and debugging.

### Bulk Operations
Efficiently manage multiple subscriptions and broker configurations.

### Real-time Monitoring
Live updates of connection status, message flow, and performance metrics.
