package com.tfkcolin.cebsscada.bluetooth

import android.Manifest
import android.app.Service
import android.bluetooth.*
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Binder
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import androidx.core.app.ActivityCompat
import dagger.hilt.android.AndroidEntryPoint
import java.io.IOException
import java.util.*
import java.util.concurrent.BlockingQueue
import java.util.concurrent.LinkedBlockingQueue

@AndroidEntryPoint
class BluetoothService : Service() {
    private val binder = LocalBinder()
    private var bluetoothAdapter: BluetoothAdapter? = null
    private var classicConnection: ClassicConnection? = null
    private var bleConnection: BleConnection? = null

    // Callback for data reception (for SCADA communication)
    var onDataReceived: ((ByteArray) -> Unit)? = null

    companion object {
        const val TAG = "BluetoothService"
        val UUID_SPP: UUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB")
    }

    inner class LocalBinder : Binder() {
        fun getService(): BluetoothService = this@BluetoothService
    }

    override fun onBind(intent: Intent): IBinder = binder

    override fun onCreate() {
        super.onCreate()
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
    }

    fun initialize(): Boolean {
        if (bluetoothAdapter == null) {
            Log.e(TAG, "Bluetooth adapter not available")
            return false
        }
        if (!bluetoothAdapter!!.isEnabled) {
            Log.w(TAG, "Bluetooth is not enabled")
            return false
        }
        return true
    }

    fun connect(device: BluetoothDevice, useBLE: Boolean) {
        Log.d(TAG, "Connect called - Device: ${device.name ?: "Unknown"} (${device.address}), useBLE: $useBLE")

        // Check permissions based on API level
        val hasConnectPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            // Android 12+: BLUETOOTH_CONNECT required
            ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android 6-11: BLUETOOTH permission required
            ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED
        }

        if (!hasConnectPermission) {
            val requiredPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) "BLUETOOTH_CONNECT" else "BLUETOOTH"
            Log.e(TAG, "$requiredPermission permission not granted")
            return
        }
        Log.d(TAG, "Bluetooth connect permission granted, proceeding with connection")
        disconnect() // Disconnect any existing connection
        if (useBLE) {
            Log.d(TAG, "Initializing BLE connection")
            bleConnection = BleConnection(device)
            bleConnection?.connect()
        } else {
            Log.d(TAG, "Initializing Classic Bluetooth connection")
            classicConnection = ClassicConnection(device)
            classicConnection?.connect()
        }
    }

    fun write(bytes: ByteArray, serviceUuid: UUID? = null, characteristicUuid: UUID? = null) {
        classicConnection?.write(bytes)
        bleConnection?.write(bytes, serviceUuid, characteristicUuid)
    }

    fun readCharacteristic(characteristic: BluetoothGattCharacteristic) {
        bleConnection?.readCharacteristic(characteristic)
    }

    fun enableNotifications(serviceUuid: UUID, characteristicUuid: UUID) {
        bleConnection?.enableNotifications(serviceUuid, characteristicUuid)
    }

    fun disableNotifications(serviceUuid: UUID, characteristicUuid: UUID) {
        bleConnection?.disableNotifications(serviceUuid, characteristicUuid)
    }

    fun getSupportedGattServices(): List<BluetoothGattService>? {
        return bleConnection?.getSupportedGattServices()
    }

    fun disconnect() {
        classicConnection?.disconnect()
        bleConnection?.disconnect()
        classicConnection = null
        bleConnection = null
    }

    // Broadcast methods removed - using callbacks and direct communication instead

    private abstract inner class Connection(protected val device: BluetoothDevice) {
        protected val handler = Handler(Looper.getMainLooper())
        protected var connectionState = BluetoothConstants.STATE_NONE

        abstract fun connect()
        abstract fun disconnect()

        protected val timeoutRunnable = Runnable { 
            if (connectionState == BluetoothConstants.STATE_CONNECTING) {
                disconnect()
                Log.e(TAG, "Connection timeout")
            }
        }

        protected fun startTimeout() {
            handler.postDelayed(timeoutRunnable, BluetoothConstants.CONNECTION_TIMEOUT)
        }

        protected fun stopTimeout() {
            handler.removeCallbacks(timeoutRunnable)
        }
    }

    private inner class ClassicConnection(device: BluetoothDevice) : Connection(device) {
        private var socket: BluetoothSocket? = null
        private var connectedThread: ConnectedThread? = null

        override fun connect() {
            Log.d(TAG, "ClassicConnection.connect() called for device: ${device.name ?: "Unknown"} (${device.address})")
            if (connectionState != BluetoothConstants.STATE_NONE) {
                Log.w(TAG, "Connection attempt ignored - already in state: $connectionState")
                return
            }
            connectionState = BluetoothConstants.STATE_CONNECTING
            startTimeout()
            Log.d(TAG, "Starting connection thread")

            Thread {
                try {
                    // Check permissions based on API level
                    val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
                    } else {
                        ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED
                    }

                    val requiredPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) "BLUETOOTH_CONNECT" else "BLUETOOTH"
                    Log.d(TAG, "Checking $requiredPermission permission in connection thread")

                    if (!hasPermission) {
                        Log.e(TAG, "$requiredPermission permission not granted in connection thread")
                        // Permission error - will be handled through callbacks
                        return@Thread
                    }
                    Log.d(TAG, "Creating RFCOMM socket with UUID: $UUID_SPP")
                    socket = device.createRfcommSocketToServiceRecord(UUID_SPP)
                    Log.d(TAG, "Socket created successfully: ${socket != null}")
                    Log.d(TAG, "Cancelling any ongoing discovery")
                    bluetoothAdapter?.cancelDiscovery()
                    Log.d(TAG, "Attempting socket connection...")
                    socket?.connect()
                    Log.d(TAG, "Socket connection successful")
                    stopTimeout()
                    connected(socket!!)
                } catch (e: IOException) {
                    Log.e(TAG, "Socket connection error", e)
                    Log.e(TAG, "Connection failed for device: ${device.name ?: "Unknown"} (${device.address})")
                    disconnect()
                    // Connection failed - will be handled through callbacks
                }
            }.start()
        }

        private fun connected(socket: BluetoothSocket) {
            connectionState = BluetoothConstants.STATE_CONNECTED
            connectedThread = ConnectedThread(socket)
            connectedThread?.start()
            Log.d(TAG, "Classic Bluetooth connection established")
        }

        fun write(bytes: ByteArray) {
            connectedThread?.write(bytes)
        }

        override fun disconnect() {
            stopTimeout()
            if (connectionState == BluetoothConstants.STATE_NONE) return
            connectionState = BluetoothConstants.STATE_NONE
            connectedThread?.cancel()
            connectedThread = null
            try {
                socket?.close()
            } catch (e: IOException) {
                Log.e(TAG, "Could not close the client socket", e)
            }
            // Disconnected - state will be communicated through callbacks
        }

        private inner class ConnectedThread(private val mmSocket: BluetoothSocket) : Thread() {
            private val mmInStream = mmSocket.inputStream
            private val mmOutStream = mmSocket.outputStream
            private val messageQueue: BlockingQueue<ByteArray> = LinkedBlockingQueue()

            override fun run() {
                val buffer = ByteArray(1024)
                var bytes: Int
                // Keep listening to the InputStream until an exception occurs
                while (connectionState == BluetoothConstants.STATE_CONNECTED) {
                    try {
                        bytes = mmInStream.read(buffer)
                        if (bytes > 0) {
                            val data = buffer.copyOf(bytes)
                            Log.d(TAG, "Received ${bytes} bytes from ${device.name ?: device.address}")
                            // For SCADA applications, we'll pass data through a listener pattern
                            onDataReceived?.invoke(data)
                        }
                    } catch (e: IOException) {
                        Log.d(TAG, "Input stream was disconnected", e)
                        disconnect()
                        Log.e(TAG, "Connection lost")
                        break
                    } catch (e: InterruptedException) {
                        break
                    }
                }
            }

            fun write(bytes: ByteArray) {
                Log.d(TAG, "Added message to queue, length: ${bytes.size} bytes")
                messageQueue.add(bytes)
            }

            fun cancel() {
                interrupt()
            }

            init {
                // Writer thread - crucial for HC-05/HC-06 communication
                Thread {
                    Log.d(TAG, "Started message writer thread for HC-05/HC-06 communication")
                    while (connectionState == BluetoothConstants.STATE_CONNECTED) {
                        try {
                            Log.d(TAG, "Waiting for message in queue...")
                            val bytes = messageQueue.take() // Blocks until a message is available
                            Log.d(TAG, "Writing ${bytes.size} bytes to HC-05/HC-06: ${String(bytes)}")
                            mmOutStream.write(bytes)
                            mmOutStream.flush() // Important for immediate transmission
                            Log.d(TAG, "Message written successfully to HC-05/HC-06")
                        } catch (e: InterruptedException) {
                            Log.d(TAG, "Writer thread interrupted")
                            break
                        } catch (e: IOException) {
                            Log.e(TAG, "Error occurred when sending data to HC-05/HC-06", e)
                            disconnect()
                            break
                        }
                    }
                    Log.d(TAG, "Message writer thread ended")
                }.start()
            }
        }
    }

    private inner class BleConnection(device: BluetoothDevice) : Connection(device) {
        private var gatt: BluetoothGatt? = null

        private val gattCallback = object : BluetoothGattCallback() {
            override fun onConnectionStateChange(gatt: BluetoothGatt, status: Int, newState: Int) {
                val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
                } else {
                    ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED
                }

                if (!hasPermission) {
                    val requiredPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) "BLUETOOTH_CONNECT" else "BLUETOOTH"
                    Log.e(TAG, "$requiredPermission permission not granted")
                    return
                }
                stopTimeout()
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    if (newState == BluetoothProfile.STATE_CONNECTED) {
                        connectionState = BluetoothConstants.STATE_CONNECTED
                        <EMAIL> = gatt
                        // Connection established - state will be communicated through callbacks
                        gatt.discoverServices()
                    } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                        disconnect()
                    }
                } else {
                    disconnect()
                    Log.e(TAG, "BLE connection failed")
                }
            }

            override fun onServicesDiscovered(gatt: BluetoothGatt, status: Int) {
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    Log.d(TAG, "BLE services discovered successfully")
                } else {
                    disconnect()
                    Log.e(TAG, "BLE services discovery failed")
                }
            }

            override fun onCharacteristicWrite(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic, status: Int) {
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    Log.d(TAG, "BLE characteristic write successful")
                } else {
                    Log.e(TAG, "BLE characteristic write failed")
                }
            }

            override fun onCharacteristicRead(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic, status: Int) {
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    characteristic.value?.let { data ->
                        Log.d(TAG, "BLE characteristic read successful, data length: ${data.size}")
                        // Data will be handled through callbacks or direct communication
                    }
                } else {
                    Log.e(TAG, "BLE characteristic read failed")
                }
            }

            override fun onCharacteristicChanged(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic, value: ByteArray) {
                Log.d(TAG, "BLE characteristic changed, data length: ${value.size}")
                // Data will be handled through callbacks or direct communication
            }

            override fun onDescriptorWrite(gatt: BluetoothGatt, descriptor: BluetoothGattDescriptor, status: Int) {
                if (status != BluetoothGatt.GATT_SUCCESS) {
                    Log.e(TAG, "BLE descriptor write failed")
                }
            }
        }

        override fun connect() {
            if (connectionState != BluetoothConstants.STATE_NONE) return

            val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
            } else {
                ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED
            }

            if (!hasPermission) {
                val requiredPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) "BLUETOOTH_CONNECT" else "BLUETOOTH"
                Log.e(TAG, "$requiredPermission permission not granted")
                return
            }
            connectionState = BluetoothConstants.STATE_CONNECTING
            startTimeout()
            gatt = device.connectGatt(this@BluetoothService, false, gattCallback)
        }

        fun write(bytes: ByteArray, serviceUuid: UUID?, characteristicUuid: UUID?) {
            if (connectionState != BluetoothConstants.STATE_CONNECTED || serviceUuid == null || characteristicUuid == null) return
            val service = gatt?.getService(serviceUuid)
            val characteristic = service?.getCharacteristic(characteristicUuid)

            if (characteristic == null) {
                Log.e(TAG, "BLE characteristic not found")
                return
            }

            // Check if characteristic supports writing
            val canWrite = (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE) != 0 ||
                          (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0

            if (!canWrite) {
                Log.e(TAG, "BLE characteristic does not support writing")
                return
            }

            val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
            } else {
                ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED
            }

            if (!hasPermission) {
                val requiredPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) "BLUETOOTH_CONNECT" else "BLUETOOTH"
                Log.e(TAG, "$requiredPermission permission not granted")
                return
            }

            // Set the value and write type
            characteristic.value = bytes
            characteristic.writeType = if ((characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0) {
                BluetoothGattCharacteristic.WRITE_TYPE_NO_RESPONSE
            } else {
                BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT
            }

            val writeResult = gatt?.writeCharacteristic(characteristic)
            if (writeResult != true) {
                Log.e(TAG, "BLE characteristic write failed")
            }
        }

        fun readCharacteristic(characteristic: BluetoothGattCharacteristic) {
            val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
            } else {
                ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED
            }

            if (!hasPermission) {
                val requiredPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) "BLUETOOTH_CONNECT" else "BLUETOOTH"
                Log.e(TAG, "$requiredPermission permission not granted")
                return
            }
            gatt?.readCharacteristic(characteristic)
        }

        fun enableNotifications(serviceUuid: UUID, characteristicUuid: UUID) {
            if (connectionState != BluetoothConstants.STATE_CONNECTED) return
            val service = gatt?.getService(serviceUuid)
            val characteristic = service?.getCharacteristic(characteristicUuid)

            if (characteristic == null) {
                Log.e(TAG, "BLE characteristic not found")
                return
            }

            // Check if characteristic supports notifications or indications
            val canNotify = (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0 ||
                           (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0

            if (!canNotify) {
                Log.e(TAG, "BLE characteristic does not support notifications")
                return
            }

            val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
            } else {
                ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED
            }

            if (!hasPermission) {
                val requiredPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) "BLUETOOTH_CONNECT" else "BLUETOOTH"
                Log.e(TAG, "$requiredPermission permission not granted")
                return
            }

            // Enable local notifications
            val notificationEnabled = gatt?.setCharacteristicNotification(characteristic, true)
            if (notificationEnabled != true) {
                Log.e(TAG, "Failed to enable BLE characteristic notification")
                return
            }

            // Write to the Client Characteristic Configuration Descriptor
            val cccdUuid = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb")
            val descriptor = characteristic.getDescriptor(cccdUuid)

            if (descriptor != null) {
                val value = if ((characteristic.properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0) {
                    BluetoothGattDescriptor.ENABLE_INDICATION_VALUE
                } else {
                    BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
                }

                descriptor.value = value
                val writeResult = gatt?.writeDescriptor(descriptor)
                if (writeResult != true) {
                    Log.e(TAG, "Failed to write BLE descriptor")
                }
            }
        }

        fun disableNotifications(serviceUuid: UUID, characteristicUuid: UUID) {
            if (connectionState != BluetoothConstants.STATE_CONNECTED) return
            val service = gatt?.getService(serviceUuid)
            val characteristic = service?.getCharacteristic(characteristicUuid)

            if (characteristic == null) {
                Log.e(TAG, "BLE characteristic not found")
                return
            }

            val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
            } else {
                ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED
            }

            if (!hasPermission) {
                val requiredPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) "BLUETOOTH_CONNECT" else "BLUETOOTH"
                Log.e(TAG, "$requiredPermission permission not granted")
                return
            }

            // Disable local notifications
            val notificationDisabled = gatt?.setCharacteristicNotification(characteristic, false)
            if (notificationDisabled != true) {
                Log.e(TAG, "Failed to disable BLE characteristic notification")
                return
            }

            // Write to the Client Characteristic Configuration Descriptor
            val cccdUuid = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb")
            val descriptor = characteristic.getDescriptor(cccdUuid)

            if (descriptor != null) {
                descriptor.value = BluetoothGattDescriptor.DISABLE_NOTIFICATION_VALUE
                val writeResult = gatt?.writeDescriptor(descriptor)
                if (writeResult != true) {
                    Log.e(TAG, "Failed to write BLE descriptor")
                }
            }
        }

        fun getSupportedGattServices(): List<BluetoothGattService>? {
            return gatt?.services
        }

        override fun disconnect() {
            stopTimeout()
            if (connectionState == BluetoothConstants.STATE_NONE) return
            connectionState = BluetoothConstants.STATE_NONE
            val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
            } else {
                ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED
            }

            if (!hasPermission) {
                val requiredPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) "BLUETOOTH_CONNECT" else "BLUETOOTH"
                Log.e(TAG, "$requiredPermission permission not granted")
                return
            }
            gatt?.disconnect()
            gatt?.close()
            gatt = null
            Log.d(TAG, "BLE connection disconnected")
        }
    }
}
