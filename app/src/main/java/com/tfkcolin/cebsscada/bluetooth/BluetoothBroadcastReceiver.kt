package com.tfkcolin.cebsscada.bluetooth

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.le.ScanResult
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build

class BluetoothBroadcastReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            BluetoothAdapter.ACTION_STATE_CHANGED -> {
                val state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR)
                handleBluetoothStateChange(context, state)
            }
            
            BluetoothDevice.ACTION_FOUND -> {
                val device: BluetoothDevice? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE, BluetoothDevice::class.java)
                } else {
                    @Suppress("DEPRECATION")
                    intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                }
                device?.let { handleDeviceFound(context, it) }
            }
            
            BluetoothAdapter.ACTION_DISCOVERY_FINISHED -> {
                handleDiscoveryFinished(context)
            }
            
            BluetoothDevice.ACTION_ACL_CONNECTED -> {
                val device: BluetoothDevice? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE, BluetoothDevice::class.java)
                } else {
                    @Suppress("DEPRECATION")
                    intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                }
                device?.let { handleDeviceConnected(context, it) }
            }
            
            BluetoothDevice.ACTION_ACL_DISCONNECTED -> {
                val device: BluetoothDevice? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE, BluetoothDevice::class.java)
                } else {
                    @Suppress("DEPRECATION")
                    intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE)
                }
                device?.let { handleDeviceDisconnected(context, it) }
            }
        }
    }

    private fun handleBluetoothStateChange(context: Context, state: Int) {
        when (state) {
            BluetoothAdapter.STATE_ON -> {
                broadcastUpdate(context, BluetoothConstants.ACTION_BLUETOOTH_ENABLED)
            }
            BluetoothAdapter.STATE_OFF -> {
                broadcastUpdate(context, BluetoothConstants.ACTION_BLUETOOTH_DISABLED)
            }
        }
    }

    private fun handleDeviceFound(context: Context, device: BluetoothDevice) {
        broadcastUpdate(context, BluetoothConstants.ACTION_DEVICE_DISCOVERED).apply {
            putExtra("DEVICE", device)
        }
    }

    private fun handleDiscoveryFinished(context: Context) {
        broadcastUpdate(context, BluetoothConstants.ACTION_DISCOVERY_FINISHED)
    }

    private fun handleDeviceConnected(context: Context, device: BluetoothDevice) {
        broadcastUpdate(context, BluetoothConstants.ACTION_DEVICE_CONNECTED).apply {
            putExtra("DEVICE", device)
        }
    }

    private fun handleDeviceDisconnected(context: Context, device: BluetoothDevice) {
        broadcastUpdate(context, BluetoothConstants.ACTION_DEVICE_DISCONNECTED).apply {
            putExtra("DEVICE", device)
        }
    }

    private fun broadcastUpdate(context: Context, action: String): Intent {
        val intent = Intent(action)
        context.sendBroadcast(intent)
        return intent
    }
}
