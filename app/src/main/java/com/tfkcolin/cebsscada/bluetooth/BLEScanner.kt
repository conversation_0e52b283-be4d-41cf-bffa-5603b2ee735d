package com.tfkcolin.cebsscada.bluetooth

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.le.BluetoothLeScanner
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanFilter
import android.bluetooth.le.ScanResult
import android.bluetooth.le.ScanSettings
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.ParcelUuid
import androidx.annotation.RequiresPermission
import java.util.*

class BLEScanner(private val context: Context, private val debugLogger: ((String) -> Unit)? = null) {

    interface BLEScanCallback {
        fun onDeviceFound(device: android.bluetooth.BluetoothDevice, rssi: Int, name: String?)
        fun onScanFinished()
        fun onScanFailed(errorCode: Int)
    }

    var bleScanCallback: BLEScanCallback? = null
    private var bluetoothAdapter: BluetoothAdapter? = null
    private var bluetoothLeScanner: BluetoothLeScanner? = null
    private var scanning = false
    private var androidScanCallback: ScanCallback? = null

    init {
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        if (bluetoothAdapter != null) {
            bluetoothLeScanner = bluetoothAdapter!!.bluetoothLeScanner
        }
    }

    fun startScan(serviceUUID: UUID? = null) {
        val message = "startScan called, scanning=$scanning"
        android.util.Log.d("BLEScanner", message)
        debugLogger?.invoke(message)
        if (scanning) {
            val alreadyScanningMessage = "Already scanning, returning"
            android.util.Log.d("BLEScanner", alreadyScanningMessage)
            debugLogger?.invoke(alreadyScanningMessage)
            return
        }

        if (bluetoothAdapter == null) {
            val adapterNullMessage = "BluetoothAdapter is null"
            android.util.Log.e("BLEScanner", adapterNullMessage)
            debugLogger?.invoke(adapterNullMessage)
            bleScanCallback?.onScanFailed(-1) // Custom error code for adapter null
            return
        }

        if (bluetoothLeScanner == null) {
            val scannerNullMessage = "BluetoothLeScanner is null, BLE not supported"
            android.util.Log.e("BLEScanner", scannerNullMessage)
            debugLogger?.invoke(scannerNullMessage)
            bleScanCallback?.onScanFailed(-2) // Custom error code for scanner null
            return
        }

        val isEnabled = bluetoothAdapter?.isEnabled ?: false
        val bluetoothEnabledMessage = "Bluetooth enabled: $isEnabled"
        android.util.Log.d("BLEScanner", bluetoothEnabledMessage)
        debugLogger?.invoke(bluetoothEnabledMessage)
        if (!isEnabled) {
            val bluetoothDisabledMessage = "Bluetooth is disabled, cannot start scan"
            android.util.Log.e("BLEScanner", bluetoothDisabledMessage)
            debugLogger?.invoke(bluetoothDisabledMessage)
            bleScanCallback?.onScanFailed(-3) // Custom error code for Bluetooth disabled
            return
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val settingUpCallbackMessage = "Setting up ScanCallback"
            android.util.Log.d("BLEScanner", settingUpCallbackMessage)
            debugLogger?.invoke(settingUpCallbackMessage)
            androidScanCallback = object : ScanCallback() {
                override fun onScanResult(callbackType: Int, result: ScanResult) {
                    super.onScanResult(callbackType, result)
                    val deviceName = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        result.scanRecord?.deviceName ?: result.device.name
                    } else {
                        result.device.name
                    }
                    val scanResultMessage = "onScanResult: device=${result.device.address}, name=$deviceName, rssi=${result.rssi}"
                    android.util.Log.d("BLEScanner", scanResultMessage)
                    debugLogger?.invoke(scanResultMessage)
                    bleScanCallback?.onDeviceFound(result.device, result.rssi, deviceName)
                }

                override fun onBatchScanResults(results: MutableList<ScanResult>) {
                    super.onBatchScanResults(results)
                    val batchResultsMessage = "onBatchScanResults: ${results.size} devices found"
                    android.util.Log.d("BLEScanner", batchResultsMessage)
                    debugLogger?.invoke(batchResultsMessage)
                    results.forEach { result ->
                        val deviceName = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            result.scanRecord?.deviceName ?: result.device.name
                        } else {
                            result.device.name
                        }
                        val batchResultMessage = "Batch result: device=${result.device.address}, name=$deviceName, rssi=${result.rssi}"
                        android.util.Log.d("BLEScanner", batchResultMessage)
                        debugLogger?.invoke(batchResultMessage)
                        bleScanCallback?.onDeviceFound(result.device, result.rssi, deviceName)
                    }
                }

                override fun onScanFailed(errorCode: Int) {
                    super.onScanFailed(errorCode)
                    val scanFailedMessage = "onScanFailed: errorCode=$errorCode"
                    android.util.Log.e("BLEScanner", scanFailedMessage)
                    debugLogger?.invoke(scanFailedMessage)
                    bleScanCallback?.onScanFailed(errorCode)
                }
            }

            val scanFilters = mutableListOf<ScanFilter>()
            serviceUUID?.let {
                scanFilters.add(ScanFilter.Builder().setServiceUuid(ParcelUuid(it)).build())
            }

            val scanSettings = ScanSettings.Builder()
                .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
                .build()

            bluetoothLeScanner?.startScan(scanFilters, scanSettings, androidScanCallback)
            scanning = true
            val scanStartedMessage = "BLE scan started successfully"
            android.util.Log.d("BLEScanner", scanStartedMessage)
            debugLogger?.invoke(scanStartedMessage)

            // Schedule a status check after 5 seconds
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                val statusCheckMessage = "Scan status check: scanning=$scanning"
                android.util.Log.d("BLEScanner", statusCheckMessage)
                debugLogger?.invoke(statusCheckMessage)
            }, 5000)
        }
    }

    fun stopScan() {
        if (!scanning) return

        bluetoothLeScanner?.stopScan(androidScanCallback)
        androidScanCallback = null
        scanning = false

        val stopScanMessage = "BLE scan stopped"
        android.util.Log.d("BLEScanner", stopScanMessage)
        debugLogger?.invoke(stopScanMessage)
        bleScanCallback?.onScanFinished()
    }

    fun isScanning(): Boolean = scanning
}
