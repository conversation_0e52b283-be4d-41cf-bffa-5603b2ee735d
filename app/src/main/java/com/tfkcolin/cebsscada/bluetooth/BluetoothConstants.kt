package com.tfkcolin.cebsscada.bluetooth

import java.util.*

object BluetoothConstants {
    // Service Actions
    const val ACTION_CONNECTING = "com.tfkcolin.cebsscada.bluetooth.CONNECTING"
    const val ACTION_CONNECTED = "com.tfkcolin.cebsscada.bluetooth.CONNECTED"
    const val ACTION_DISCONNECTED = "com.tfkcolin.cebsscada.bluetooth.DISCONNECTED"
    const val ACTION_CONNECTION_FAILED = "com.tfkcolin.cebsscada.bluetooth.CONNECTION_FAILED"
    const val ACTION_CONNECTION_LOST = "com.tfkcolin.cebsscada.bluetooth.CONNECTION_LOST"
    const val ACTION_MESSAGE_RECEIVED = "com.tfkcolin.cebsscada.bluetooth.MESSAGE_RECEIVED"
    const val ACTION_MESSAGE_SENT = "com.tfkcolin.cebsscada.bluetooth.MESSAGE_SENT"
    const val ACTION_WRITE_FAILED = "com.tfkcolin.cebsscada.bluetooth.WRITE_FAILED"
    
    // Receiver Actions
    const val ACTION_BLUETOOTH_ENABLED = "com.tfkcolin.cebsscada.bluetooth.BLUETOOTH_ENABLED"
    const val ACTION_BLUETOOTH_DISABLED = "com.tfkcolin.cebsscada.bluetooth.BLUETOOTH_DISABLED"
    const val ACTION_DEVICE_DISCOVERED = "com.tfkcolin.cebsscada.bluetooth.DEVICE_DISCOVERED"
    const val ACTION_DISCOVERY_FINISHED = "com.tfkcolin.cebsscada.bluetooth.DISCOVERY_FINISHED"
    const val ACTION_DEVICE_CONNECTED = "com.tfkcolin.cebsscada.bluetooth.DEVICE_CONNECTED"
    const val ACTION_DEVICE_DISCONNECTED = "com.tfkcolin.cebsscada.bluetooth.DEVICE_DISCONNECTED"

    // BLE Actions
    const val ACTION_BLE_DEVICE_DISCOVERED = "com.tfkcolin.cebsscada.bluetooth.BLE_DEVICE_DISCOVERED"
    const val ACTION_BLE_SERVICES_DISCOVERED = "com.tfkcolin.cebsscada.bluetooth.BLE_SERVICES_DISCOVERED"

    // Intent Extras
    const val EXTRA_DEVICE_ADDRESS = "com.tfkcolin.cebsscada.bluetooth.EXTRA_DEVICE_ADDRESS"
    const val EXTRA_ERROR_CODE = "com.tfkcolin.cebsscada.bluetooth.EXTRA_ERROR_CODE"
    const val EXTRA_ERROR_MESSAGE = "com.tfkcolin.cebsscada.bluetooth.EXTRA_ERROR_MESSAGE"
    const val EXTRA_MESSAGE = "com.tfkcolin.cebsscada.bluetooth.EXTRA_MESSAGE"
    const val EXTRA_DATA = "com.tfkcolin.cebsscada.bluetooth.EXTRA_DATA"

    // Connection States
    const val STATE_NONE = 0
    const val STATE_CONNECTING = 1
    const val STATE_CONNECTED = 2

    // Error Codes
    const val ERROR_NONE = 0
    const val ERROR_CONNECTION_TIMEOUT = 1
    const val ERROR_CONNECTION_FAILED = 2
    const val ERROR_CONNECTION_LOST = 3
    const val ERROR_WRITE_FAILED = 4
    const val ERROR_BLUETOOTH_NOT_AVAILABLE = 5
    const val ERROR_BLUETOOTH_NOT_ENABLED = 6
    const val ERROR_DEVICE_NOT_FOUND = 7
    const val ERROR_INVALID_ADDRESS = 8
    const val ERROR_BLE_NOT_SUPPORTED = 9
    const val ERROR_BLE_SERVICES_NOT_FOUND = 10
    const val ERROR_MISSING_PERMISSION = 11
    
    // Error Messages
    val ERROR_MESSAGES = mapOf(
        ERROR_NONE to "No error",
        ERROR_CONNECTION_TIMEOUT to "Connection timeout",
        ERROR_CONNECTION_FAILED to "Connection failed",
        ERROR_CONNECTION_LOST to "Connection lost",
        ERROR_WRITE_FAILED to "Write failed",
        ERROR_BLUETOOTH_NOT_AVAILABLE to "Bluetooth not available",
        ERROR_BLUETOOTH_NOT_ENABLED to "Bluetooth not enabled",
        ERROR_DEVICE_NOT_FOUND to "Device not found",
        ERROR_INVALID_ADDRESS to "Invalid device address",
        ERROR_BLE_NOT_SUPPORTED to "BLE not supported",
        ERROR_BLE_SERVICES_NOT_FOUND to "BLE services not found",
        ERROR_MISSING_PERMISSION to "Missing Bluetooth permission"
    )
    
    // Connection Timeout in milliseconds
    const val CONNECTION_TIMEOUT = 10000L // 10 seconds
}
