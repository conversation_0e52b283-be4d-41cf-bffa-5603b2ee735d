package com.tfkcolin.cebsscada.ui.bluetooth

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Bluetooth
import androidx.compose.material.icons.filled.BluetoothDisabled
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.bluetooth.BluetoothState

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BluetoothStatusBar(
    isEnabled: Boolean,
    state: BluetoothState,
    onEnable: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 4.dp, vertical = 4.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isEnabled) {
                when (state) {
                    is BluetoothState.Connected -> MaterialTheme.colorScheme.primaryContainer
                    is BluetoothState.Error -> MaterialTheme.colorScheme.errorContainer
                    else -> MaterialTheme.colorScheme.surface
                }
            } else MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.weight(1f)
            ) {
                Icon(
                    imageVector = if (isEnabled) Icons.Default.Bluetooth else Icons.Default.BluetoothDisabled,
                    contentDescription = if (isEnabled) "Bluetooth Enabled" else "Bluetooth Disabled",
                    tint = if (isEnabled) {
                        when (state) {
                            is BluetoothState.Connected -> MaterialTheme.colorScheme.onPrimaryContainer
                            is BluetoothState.Error -> MaterialTheme.colorScheme.onErrorContainer
                            else -> MaterialTheme.colorScheme.primary
                        }
                    } else MaterialTheme.colorScheme.onErrorContainer
                )

                Spacer(modifier = Modifier.width(12.dp))

                Column {
                    Text(
                        text = "Bluetooth Status",
                        style = MaterialTheme.typography.labelMedium,
                        color = if (isEnabled) {
                            when (state) {
                                is BluetoothState.Connected -> MaterialTheme.colorScheme.onPrimaryContainer
                                is BluetoothState.Error -> MaterialTheme.colorScheme.onErrorContainer
                                else -> MaterialTheme.colorScheme.onSurface
                            }
                        } else MaterialTheme.colorScheme.onErrorContainer
                    )

                    when (state) {
                        is BluetoothState.Error -> {
                            Text(
                                text = state.message,
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                        }
                        is BluetoothState.Connecting -> {
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(12.dp),
                                    strokeWidth = 1.5.dp,
                                    color = MaterialTheme.colorScheme.primary
                                )
                                Spacer(modifier = Modifier.width(6.dp))
                                Text(
                                    text = "Connecting...",
                                    style = MaterialTheme.typography.labelSmall,
                                    color = MaterialTheme.colorScheme.onSurface
                                )
                            }
                        }
                        is BluetoothState.Connected -> {
                            Text(
                                text = "Connected to ${state.device.name ?: "Unknown Device"}",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        }
                        else -> {
                            Text(
                                text = if (isEnabled) "Ready to connect" else "Bluetooth not available",
                                style = MaterialTheme.typography.labelSmall,
                                color = if (isEnabled) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                                       else MaterialTheme.colorScheme.onErrorContainer
                            )
                        }
                    }
                }
            }

            if (!isEnabled) {
                Button(
                    onClick = onEnable,
                    modifier = Modifier.height(36.dp)
                ) {
                    Text(
                        text = "Enable",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
    }
}
