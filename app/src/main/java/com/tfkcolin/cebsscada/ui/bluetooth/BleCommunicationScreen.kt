package com.tfkcolin.cebsscada.ui.bluetooth

import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattService
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material.icons.filled.DataUsage
import androidx.compose.material.icons.filled.List
import androidx.compose.material.icons.filled.PowerOff
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Send
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Sync
import androidx.compose.material.icons.filled.Timer
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BleCommunicationScreen(
    device: BluetoothDevice,
    services: List<BluetoothGattService>,
    onDisconnect: () -> Unit,
    onReadCharacteristic: (BluetoothGattCharacteristic) -> Unit,
    onWriteCharacteristic: (BluetoothGattCharacteristic, String) -> Unit,
    onEnableNotifications: (BluetoothGattCharacteristic) -> Unit,
    onDisableNotifications: (BluetoothGattCharacteristic) -> Unit
) {
    var showWriteDialog by remember { mutableStateOf<BluetoothGattCharacteristic?>(null) }
    var selectedTabIndex by remember { mutableStateOf(0) }
    var receivedData by remember { mutableStateOf<List<Pair<String, String>>>(emptyList()) }
    var connectionUptime by remember { mutableStateOf(0L) }

    val tabs = listOf("Device Control", "Services & Characteristics", "Live Data Monitor", "SCADA Commands")

    // Track connection uptime
    LaunchedEffect(Unit) {
        val startTime = System.currentTimeMillis()
        while (true) {
            connectionUptime = System.currentTimeMillis() - startTime
            kotlinx.coroutines.delay(1000)
        }
    }

    Column(modifier = Modifier.fillMaxSize()) {
        // Enhanced SCADA-style header
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(modifier = Modifier.weight(1f)) {
                        Text(
                            text = "BLE SCADA Interface",
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        Text(
                            text = device.name ?: "Unknown BLE Device",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "MAC: ${device.address}",
                            style = MaterialTheme.typography.bodyMedium,
                            fontFamily = FontFamily.Monospace,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }

                    Column(horizontalAlignment = Alignment.End) {
                        // Connection status indicator
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.CheckCircle,
                                contentDescription = "Connected",
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(24.dp)
                            )
                            Text(
                                text = "CONNECTED",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        Button(
                            onClick = onDisconnect,
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Icon(Icons.Default.PowerOff, contentDescription = null, modifier = Modifier.size(16.dp))
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Disconnect")
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // SCADA-style status indicators
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    ScadaStatusChip(
                        label = "Services",
                        value = services.size.toString(),
                        icon = Icons.AutoMirrored.Filled.List,
                        status = if (services.isNotEmpty()) "ACTIVE" else "INACTIVE"
                    )
                    ScadaStatusChip(
                        label = "Characteristics",
                        value = services.sumOf { it.characteristics.size }.toString(),
                        icon = Icons.Default.Settings,
                        status = "READY"
                    )
                    ScadaStatusChip(
                        label = "Uptime",
                        value = "${connectionUptime / 1000}s",
                        icon = Icons.Default.Timer,
                        status = "RUNNING"
                    )
                    ScadaStatusChip(
                        label = "Data Points",
                        value = receivedData.size.toString(),
                        icon = Icons.Default.DataUsage,
                        status = if (receivedData.isNotEmpty()) "RECEIVING" else "STANDBY"
                    )
                }
            }
        }

        // Tab Row with SCADA styling
        TabRow(
            selectedTabIndex = selectedTabIndex,
            containerColor = MaterialTheme.colorScheme.surface,
            contentColor = MaterialTheme.colorScheme.onSurface
        ) {
            tabs.forEachIndexed { index, title ->
                Tab(
                    selected = selectedTabIndex == index,
                    onClick = { selectedTabIndex = index },
                    text = {
                        Text(
                            text = title,
                            fontWeight = if (selectedTabIndex == index) FontWeight.Bold else FontWeight.Normal
                        )
                    }
                )
            }
        }

        // Tab Content
        when (selectedTabIndex) {
            0 -> DeviceControlTab(
                device = device,
                services = services,
                onDisconnect = onDisconnect
            )
            1 -> ServicesCharacteristicsTab(
                services = services,
                onWriteClick = { showWriteDialog = it },
                onReadClick = onReadCharacteristic,
                onEnableNotifications = onEnableNotifications,
                onDisableNotifications = onDisableNotifications
            )
            2 -> LiveDataMonitorTab(
                receivedData = receivedData,
                onClearData = { receivedData = emptyList() }
            )
            3 -> ScadaCommandsTab(
                services = services,
                onSendCommand = { serviceUuid, characteristicUuid, data ->
                    services.find { it.uuid.toString() == serviceUuid }
                        ?.characteristics?.find { it.uuid.toString() == characteristicUuid }
                        ?.let { characteristic ->
                            onWriteCharacteristic(characteristic, data)
                            // Add to received data for monitoring
                            receivedData = receivedData + Pair("SENT: $characteristicUuid", data)
                        }
                }
            )
        }
    }

    showWriteDialog?.let {
        WriteCharacteristicDialog(
            characteristic = it,
            onDismiss = { showWriteDialog = null },
            onWrite = { value ->
                onWriteCharacteristic(it, value)
                // Add to received data for monitoring
                receivedData = receivedData + Pair("WRITE: ${it.uuid}", value)
                showWriteDialog = null
            }
        )
    }
}

@Composable
private fun ScadaStatusChip(
    label: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    status: String
) {
    val statusColor = when (status) {
        "ACTIVE", "READY", "RUNNING", "RECEIVING" -> MaterialTheme.colorScheme.primary
        "INACTIVE", "STANDBY" -> MaterialTheme.colorScheme.outline
        "ERROR" -> MaterialTheme.colorScheme.error
        else -> MaterialTheme.colorScheme.secondary
    }

    Card(
        colors = CardDefaults.cardColors(
            containerColor = statusColor.copy(alpha = 0.1f)
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = statusColor,
                    modifier = Modifier.size(16.dp)
                )
                Text(
                    text = value,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = statusColor
                )
            }
            Text(
                text = label,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
            Text(
                text = status,
                style = MaterialTheme.typography.labelSmall,
                color = statusColor,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun DeviceControlTab(
    device: BluetoothDevice,
    services: List<BluetoothGattService>,
    onDisconnect: () -> Unit
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        item {
            Text(
                text = "Device Information",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
        }

        item {
            Card(modifier = Modifier.fillMaxWidth()) {
                Column(modifier = Modifier.padding(16.dp)) {
                    DeviceInfoRow("Device Name", device.name ?: "Unknown")
                    DeviceInfoRow("MAC Address", device.address)
                    DeviceInfoRow("Device Type", "Bluetooth Low Energy")
                    DeviceInfoRow("Bond State", when (device.bondState) {
                        BluetoothDevice.BOND_BONDED -> "Bonded"
                        BluetoothDevice.BOND_BONDING -> "Bonding"
                        else -> "Not Bonded"
                    })
                    DeviceInfoRow("Services Count", services.size.toString())
                    DeviceInfoRow("Total Characteristics", services.sumOf { it.characteristics.size }.toString())
                }
            }
        }

        item {
            Text(
                text = "Connection Controls",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
        }

        item {
            Card(modifier = Modifier.fillMaxWidth()) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Button(
                            onClick = onDisconnect,
                            modifier = Modifier.weight(1f),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.error
                            )
                        ) {
                            Icon(Icons.Default.PowerOff, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Disconnect")
                        }

                        Button(
                            onClick = { /* TODO: Implement reconnect */ },
                            modifier = Modifier.weight(1f)
                        ) {
                            Icon(Icons.Default.Refresh, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Reconnect")
                        }
                    }

                    Button(
                        onClick = { /* TODO: Implement service refresh */ },
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.secondary
                        )
                    ) {
                        Icon(Icons.Default.Sync, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Refresh Services")
                    }
                }
            }
        }

        if (services.isNotEmpty()) {
            item {
                Text(
                    text = "Quick Service Overview",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold
                )
            }

            items(services.take(5)) { service ->
                Card(modifier = Modifier.fillMaxWidth()) {
                    Column(modifier = Modifier.padding(12.dp)) {
                        Text(
                            text = getServiceName(service.uuid),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = service.uuid.toString(),
                            style = MaterialTheme.typography.bodySmall,
                            fontFamily = FontFamily.Monospace,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                        Text(
                            text = "${service.characteristics.size} characteristics available",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun DeviceInfoRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun ServiceItem(
    service: BluetoothGattService,
    onReadClick: (BluetoothGattCharacteristic) -> Unit,
    onWriteClick: (BluetoothGattCharacteristic) -> Unit,
    onEnableNotifications: (BluetoothGattCharacteristic) -> Unit,
    onDisableNotifications: (BluetoothGattCharacteristic) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }

    Card(modifier = Modifier.fillMaxWidth().padding(vertical = 8.dp)) {
        Column(modifier = Modifier.clickable { expanded = !expanded }.padding(16.dp)) {
            Text("Service: ${service.uuid}", style = MaterialTheme.typography.titleMedium)
            if (expanded) {
                service.characteristics.forEach { characteristic ->
                    CharacteristicItem(characteristic, onReadClick, onWriteClick, onEnableNotifications, onDisableNotifications)
                }
            }
        }
    }
}

@Composable
private fun CharacteristicItem(
    characteristic: BluetoothGattCharacteristic,
    onReadClick: (BluetoothGattCharacteristic) -> Unit,
    onWriteClick: (BluetoothGattCharacteristic) -> Unit,
    onEnableNotifications: (BluetoothGattCharacteristic) -> Unit,
    onDisableNotifications: (BluetoothGattCharacteristic) -> Unit
) {
    var isNotifying by remember { mutableStateOf(false) }
    var lastValue by remember { mutableStateOf<ByteArray?>(null) }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 16.dp, top = 8.dp, end = 16.dp)
    ) {
        Column(modifier = Modifier.padding(12.dp)) {
            Text(
                text = "Characteristic: ${characteristic.uuid}",
                style = MaterialTheme.typography.titleSmall
            )

            // Properties display
            val properties = mutableListOf<String>()
            if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_READ != 0) properties.add("Read")
            if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE != 0) properties.add("Write")
            if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE != 0) properties.add("Write No Response")
            if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY != 0) properties.add("Notify")
            if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_INDICATE != 0) properties.add("Indicate")

            Text(
                text = "Properties: ${properties.joinToString(", ")}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )

            // Last value display
            lastValue?.let { value ->
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "Last Value: ${value.joinToString(" ") { "%02X".format(it) }} (${String(value)})",
                    style = MaterialTheme.typography.bodySmall,
                    fontFamily = FontFamily.Monospace,
                    color = MaterialTheme.colorScheme.primary
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_READ != 0) {
                    Button(
                        onClick = { onReadClick(characteristic) },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Read")
                    }
                }
                if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE != 0 ||
                    characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE != 0) {
                    Button(
                        onClick = { onWriteClick(characteristic) },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("Write")
                    }
                }
                if (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY != 0 ||
                    characteristic.properties and BluetoothGattCharacteristic.PROPERTY_INDICATE != 0) {
                    Button(
                        onClick = {
                            if (isNotifying) {
                                onDisableNotifications(characteristic)
                            } else {
                                onEnableNotifications(characteristic)
                            }
                            isNotifying = !isNotifying
                        },
                        modifier = Modifier.weight(1f),
                        colors = if (isNotifying) {
                            ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.secondary)
                        } else {
                            ButtonDefaults.buttonColors()
                        }
                    ) {
                        Text(if (isNotifying) "Stop Notify" else "Start Notify")
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun WriteCharacteristicDialog(
    characteristic: BluetoothGattCharacteristic,
    onDismiss: () -> Unit,
    onWrite: (String) -> Unit
) {
    var value by remember { mutableStateOf("") }
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Write to ${characteristic.uuid}") },
        text = { OutlinedTextField(value = value, onValueChange = { value = it }, label = { Text("Value") }) },
        confirmButton = {
            Button(onClick = { onWrite(value) }) {
                Text("Write")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
private fun ServicesTab(
    services: List<BluetoothGattService>,
    onWriteClick: (BluetoothGattCharacteristic) -> Unit,
    onReadClick: (BluetoothGattCharacteristic) -> Unit,
    onEnableNotifications: (BluetoothGattCharacteristic) -> Unit,
    onDisableNotifications: (BluetoothGattCharacteristic) -> Unit
) {
    if (services.isEmpty()) {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                CircularProgressIndicator()
                Spacer(modifier = Modifier.height(8.dp))
                Text("Discovering services...", style = MaterialTheme.typography.bodyLarge)
            }
        }
    } else {
        LazyColumn(modifier = Modifier.padding(horizontal = 16.dp)) {
            items(services) { service ->
                ServiceItem(
                    service = service,
                    onWriteClick = onWriteClick,
                    onReadClick = onReadClick,
                    onEnableNotifications = onEnableNotifications,
                    onDisableNotifications = onDisableNotifications
                )
            }
        }
    }
}

@Composable
private fun DataTab() {
    var receivedData by remember { mutableStateOf(listOf<String>()) }

    Column(modifier = Modifier.padding(16.dp)) {
        Text(
            text = "Received Data",
            style = MaterialTheme.typography.headlineSmall
        )
        Spacer(modifier = Modifier.height(8.dp))

        if (receivedData.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "No data received yet",
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        } else {
            LazyColumn {
                items(receivedData) { data ->
                    DataItem(data = data)
                }
            }
        }
    }
}

@Composable
private fun DataItem(data: String) {
    val clipboardManager = LocalClipboardManager.current

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            SelectionContainer(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = data,
                    fontFamily = FontFamily.Monospace,
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            IconButton(
                onClick = {
                    clipboardManager.setText(AnnotatedString(data))
                }
            ) {
                Icon(
                    imageVector = Icons.Default.ContentCopy,
                    contentDescription = "Copy"
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun RawCommandsTab(
    onSendCommand: (String, String, String) -> Unit
) {
    var serviceUuid by remember { mutableStateOf("") }
    var characteristicUuid by remember { mutableStateOf("") }
    var dataToSend by remember { mutableStateOf("") }
    var dataFormat by remember { mutableStateOf("Text") }
    val dataFormats = listOf("Text", "Hex", "Bytes")
    var expanded by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            text = "Raw BLE Commands",
            style = MaterialTheme.typography.headlineSmall
        )
        Spacer(modifier = Modifier.height(16.dp))

        // Service UUID Input
        OutlinedTextField(
            value = serviceUuid,
            onValueChange = { serviceUuid = it },
            label = { Text("Service UUID") },
            placeholder = { Text("e.g., 0000180f-0000-1000-8000-00805f9b34fb") },
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.height(8.dp))

        // Characteristic UUID Input
        OutlinedTextField(
            value = characteristicUuid,
            onValueChange = { characteristicUuid = it },
            label = { Text("Characteristic UUID") },
            placeholder = { Text("e.g., 00002a19-0000-1000-8000-00805f9b34fb") },
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.height(8.dp))

        // Data Format Dropdown
        ExposedDropdownMenuBox(
            expanded = expanded,
            onExpandedChange = { expanded = !expanded }
        ) {
            OutlinedTextField(
                value = dataFormat,
                onValueChange = { },
                readOnly = true,
                label = { Text("Data Format") },
                trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                modifier = Modifier
                    .fillMaxWidth()
                    .menuAnchor()
            )
            ExposedDropdownMenu(
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                dataFormats.forEach { format ->
                    DropdownMenuItem(
                        text = { Text(format) },
                        onClick = {
                            dataFormat = format
                            expanded = false
                        }
                    )
                }
            }
        }
        Spacer(modifier = Modifier.height(8.dp))

        // Data Input
        OutlinedTextField(
            value = dataToSend,
            onValueChange = { dataToSend = it },
            label = { Text("Data to Send") },
            placeholder = {
                Text(when (dataFormat) {
                    "Text" -> "Hello World"
                    "Hex" -> "48656C6C6F20576F726C64"
                    "Bytes" -> "72,101,108,108,111"
                    else -> "Enter data"
                })
            },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3
        )
        Spacer(modifier = Modifier.height(16.dp))

        // Send Button
        Button(
            onClick = {
                if (serviceUuid.isNotBlank() && characteristicUuid.isNotBlank() && dataToSend.isNotBlank()) {
                    val processedData = when (dataFormat) {
                        "Hex" -> {
                            // Convert hex string to text
                            try {
                                dataToSend.chunked(2)
                                    .map { it.toInt(16).toChar() }
                                    .joinToString("")
                            } catch (e: Exception) {
                                dataToSend // Fallback to original if conversion fails
                            }
                        }
                        "Bytes" -> {
                            // Convert comma-separated bytes to text
                            try {
                                dataToSend.split(",")
                                    .map { it.trim().toInt().toChar() }
                                    .joinToString("")
                            } catch (e: Exception) {
                                dataToSend // Fallback to original if conversion fails
                            }
                        }
                        else -> dataToSend
                    }
                    onSendCommand(serviceUuid, characteristicUuid, processedData)
                }
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = serviceUuid.isNotBlank() && characteristicUuid.isNotBlank() && dataToSend.isNotBlank()
        ) {
            Icon(
                imageVector = Icons.Default.Send,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("Send Command")
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Common UUIDs Reference
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "Common Service UUIDs",
                    style = MaterialTheme.typography.titleMedium
                )
                Spacer(modifier = Modifier.height(8.dp))

                val commonServices = listOf(
                    "Battery Service" to "0000180f-0000-1000-8000-00805f9b34fb",
                    "Device Information" to "0000180a-0000-1000-8000-00805f9b34fb",
                    "Generic Access" to "00001800-0000-1000-8000-00805f9b34fb",
                    "Generic Attribute" to "00001801-0000-1000-8000-00805f9b34fb",
                    "Heart Rate" to "0000180d-0000-1000-8000-00805f9b34fb"
                )

                commonServices.forEach { (name, uuid) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { serviceUuid = uuid }
                            .padding(vertical = 4.dp),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = name,
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = uuid.take(8) + "...",
                            style = MaterialTheme.typography.bodySmall,
                            fontFamily = FontFamily.Monospace,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ServicesCharacteristicsTab(
    services: List<BluetoothGattService>,
    onWriteClick: (BluetoothGattCharacteristic) -> Unit,
    onReadClick: (BluetoothGattCharacteristic) -> Unit,
    onEnableNotifications: (BluetoothGattCharacteristic) -> Unit,
    onDisableNotifications: (BluetoothGattCharacteristic) -> Unit
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item {
            Text(
                text = "BLE Services & Characteristics",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "Interact with device services and characteristics",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        }

        if (services.isEmpty()) {
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f)
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Warning,
                            contentDescription = null,
                            modifier = Modifier.size(48.dp),
                            tint = MaterialTheme.colorScheme.error
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "No Services Discovered",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "Make sure the device is properly connected",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                }
            }
        } else {
            items(services) { service ->
                ServiceItem(
                    service = service,
                    onReadClick = onReadClick,
                    onWriteClick = onWriteClick,
                    onEnableNotifications = onEnableNotifications,
                    onDisableNotifications = onDisableNotifications
                )
            }
        }
    }
}

@Composable
private fun LiveDataMonitorTab(
    receivedData: List<Pair<String, String>>,
    onClearData: () -> Unit
) {
    Column(modifier = Modifier.fillMaxSize()) {
        // Header with controls
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "Live Data Monitor",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "${receivedData.size} data points received",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                }

                Button(
                    onClick = onClearData,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(Icons.Default.Clear, contentDescription = null, modifier = Modifier.size(16.dp))
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Clear")
                }
            }
        }

        // Data display
        if (receivedData.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Icon(
                        imageVector = Icons.Default.DataUsage,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "No Data Received",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                    )
                    Text(
                        text = "Data will appear here when received from the device",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f)
                    )
                }
            }
        } else {
            LazyColumn(
                modifier = Modifier.padding(horizontal = 16.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(receivedData.reversed()) { (source, data) ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = if (source.startsWith("SENT:")) {
                                MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                            } else {
                                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                            }
                        )
                    ) {
                        Column(modifier = Modifier.padding(12.dp)) {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = source,
                                    style = MaterialTheme.typography.labelMedium,
                                    fontWeight = FontWeight.Bold,
                                    color = if (source.startsWith("SENT:")) {
                                        MaterialTheme.colorScheme.secondary
                                    } else {
                                        MaterialTheme.colorScheme.primary
                                    }
                                )
                                Text(
                                    text = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault())
                                        .format(java.util.Date()),
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                )
                            }
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = data,
                                style = MaterialTheme.typography.bodyMedium,
                                fontFamily = FontFamily.Monospace
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ScadaCommandsTab(
    services: List<BluetoothGattService>,
    onSendCommand: (String, String, String) -> Unit
) {
    var selectedService by remember { mutableStateOf<BluetoothGattService?>(null) }
    var selectedCharacteristic by remember { mutableStateOf<BluetoothGattCharacteristic?>(null) }
    var commandData by remember { mutableStateOf("") }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "SCADA Command Interface",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "Send commands and data to connected BLE device",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
        }

        // Quick Commands
        item {
            Card(modifier = Modifier.fillMaxWidth()) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "Quick SCADA Commands",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))

                    val quickCommands = listOf(
                        "Read Status" to "STATUS?",
                        "Get Version" to "VERSION?",
                        "Reset Device" to "RESET",
                        "Start Monitoring" to "START",
                        "Stop Monitoring" to "STOP"
                    )

                    quickCommands.forEach { (name, command) ->
                        Button(
                            onClick = { commandData = command },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 2.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.secondary
                            )
                        ) {
                            Text("$name: $command")
                        }
                    }
                }
            }
        }
    }
}

// Utility functions for BLE service and characteristic names
private fun getServiceName(uuid: UUID): String {
    return when (uuid.toString().uppercase()) {
        "0000180F-0000-1000-8000-00805F9B34FB" -> "Battery Service"
        "0000180A-0000-1000-8000-00805F9B34FB" -> "Device Information"
        "00001800-0000-1000-8000-00805F9B34FB" -> "Generic Access"
        "00001801-0000-1000-8000-00805F9B34FB" -> "Generic Attribute"
        "0000180D-0000-1000-8000-00805F9B34FB" -> "Heart Rate"
        "00001809-0000-1000-8000-00805F9B34FB" -> "Health Thermometer"
        "0000181A-0000-1000-8000-00805F9B34FB" -> "Environmental Sensing"
        else -> "Unknown Service"
    }
}

private fun getCharacteristicName(uuid: UUID): String {
    return when (uuid.toString().uppercase()) {
        "00002A19-0000-1000-8000-00805F9B34FB" -> "Battery Level"
        "00002A29-0000-1000-8000-00805F9B34FB" -> "Manufacturer Name"
        "00002A24-0000-1000-8000-00805F9B34FB" -> "Model Number"
        "00002A25-0000-1000-8000-00805F9B34FB" -> "Serial Number"
        "00002A27-0000-1000-8000-00805F9B34FB" -> "Hardware Revision"
        "00002A26-0000-1000-8000-00805F9B34FB" -> "Firmware Revision"
        "00002A28-0000-1000-8000-00805F9B34FB" -> "Software Revision"
        "00002A37-0000-1000-8000-00805F9B34FB" -> "Heart Rate Measurement"
        "00002A38-0000-1000-8000-00805F9B34FB" -> "Body Sensor Location"
        "00002A39-0000-1000-8000-00805F9B34FB" -> "Heart Rate Control Point"
        "00002A1C-0000-1000-8000-00805F9B34FB" -> "Temperature Measurement"
        "00002A1E-0000-1000-8000-00805F9B34FB" -> "Intermediate Temperature"
        else -> "Unknown Characteristic"
    }
}
