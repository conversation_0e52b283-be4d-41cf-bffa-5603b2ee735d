package com.tfkcolin.cebsscada.ui.testing

import com.tfkcolin.cebsscada.communication.models.MqttBroker

/**
 * Predefined MQTT test scenarios for comprehensive testing
 */
object MqttTestScenarios {
    
    /**
     * Common public MQTT brokers for testing
     */
    val publicBrokers = listOf(
        MqttBroker(
            id = "test.mosquitto.org:1883",
            name = "Eclipse Mosquitto Test Server",
            address = "test.mosquitto.org",
            port = 1883,
            username = null,
            password = null,
            useTls = false,
            clientId = null
        ),
        MqttBroker(
            id = "mqtt.eclipseprojects.io:1883",
            name = "Eclipse IoT Test Server",
            address = "mqtt.eclipseprojects.io",
            port = 1883,
            username = null,
            password = null,
            useTls = false,
            clientId = null
        ),
        MqttBroker(
            id = "broker.hivemq.com:1883",
            name = "HiveMQ Public Broker",
            address = "broker.hivemq.com",
            port = 1883,
            username = null,
            password = null,
            useTls = false,
            clientId = null
        ),
        MqttBroker(
            id = "test.mosquitto.org:8883",
            name = "Eclipse Mosquitto TLS Test Server",
            address = "test.mosquitto.org",
            port = 8883,
            username = null,
            password = null,
            useTls = true,
            clientId = null
        )
    )
    
    /**
     * Local development brokers
     */
    val localBrokers = listOf(
        MqttBroker(
            id = "localhost:1883",
            name = "Local MQTT Broker",
            address = "localhost",
            port = 1883,
            username = null,
            password = null,
            useTls = false,
            clientId = null
        ),
        MqttBroker(
            id = "*************:1883",
            name = "Local Network Broker",
            address = "*************",
            port = 1883,
            username = null,
            password = null,
            useTls = false,
            clientId = null
        ),
        MqttBroker(
            id = "localhost:1883-auth",
            name = "Local Authenticated Broker",
            address = "localhost",
            port = 1883,
            username = "testuser",
            password = "testpass",
            useTls = false,
            clientId = null
        )
    )
    
    /**
     * Test message patterns for different scenarios
     */
    val testMessages = listOf(
        TestMessage(
            name = "Simple Text",
            topic = "test/simple",
            payload = "Hello MQTT World!",
            description = "Basic text message for connectivity testing"
        ),
        TestMessage(
            name = "JSON Sensor Data",
            topic = "sensors/temperature",
            payload = """{"sensor_id": "temp_01", "temperature": 23.5, "humidity": 65.2, "timestamp": ${System.currentTimeMillis()}}""",
            description = "Simulated sensor data in JSON format"
        ),
        TestMessage(
            name = "Device Status",
            topic = "devices/status",
            payload = """{"device_id": "scada_device_01", "status": "online", "battery": 85, "signal_strength": -45}""",
            description = "Device status information"
        ),
        TestMessage(
            name = "Control Command",
            topic = "control/relay",
            payload = """{"command": "toggle", "relay_id": "relay_01", "timestamp": ${System.currentTimeMillis()}}""",
            description = "Control command for relay switching"
        ),
        TestMessage(
            name = "Alarm Notification",
            topic = "alarms/critical",
            payload = """{"alarm_id": "temp_high", "severity": "critical", "message": "Temperature exceeded threshold", "value": 85.3}""",
            description = "Critical alarm notification"
        ),
        TestMessage(
            name = "Large Payload",
            topic = "test/large",
            payload = "A".repeat(1000) + " - Large payload test message",
            description = "Test message with large payload (1KB+)"
        ),
        TestMessage(
            name = "Special Characters",
            topic = "test/special",
            payload = "Special chars: àáâãäåæçèéêë ñòóôõö ùúûüý ÿ 中文 العربية 🚀🔧⚡",
            description = "Message with special characters and emojis"
        )
    )
    
    /**
     * Common topic patterns for subscription testing
     */
    val testTopics = listOf(
        TestTopic(
            pattern = "test/+",
            description = "Single-level wildcard - matches any single topic level",
            examples = listOf("test/temperature", "test/humidity", "test/pressure")
        ),
        TestTopic(
            pattern = "sensors/#",
            description = "Multi-level wildcard - matches all subtopics",
            examples = listOf("sensors/temp/room1", "sensors/humidity/outdoor", "sensors/pressure/basement/zone1")
        ),
        TestTopic(
            pattern = "devices/+/status",
            description = "Device status monitoring pattern",
            examples = listOf("devices/device01/status", "devices/gateway/status", "devices/sensor123/status")
        ),
        TestTopic(
            pattern = "alarms/+/+",
            description = "Alarm monitoring with severity and type",
            examples = listOf("alarms/critical/temperature", "alarms/warning/battery", "alarms/info/maintenance")
        ),
        TestTopic(
            pattern = "control/+/command",
            description = "Control command pattern",
            examples = listOf("control/relay01/command", "control/motor/command", "control/valve/command")
        )
    )
    
    /**
     * Automated test sequences
     */
    val testSequences = listOf(
        TestSequence(
            name = "Basic Connectivity Test",
            description = "Test basic MQTT connection and messaging",
            steps = listOf(
                TestStep("Connect to broker", "Establish connection to MQTT broker"),
                TestStep("Subscribe to test topic", "Subscribe to 'test/connectivity'"),
                TestStep("Publish test message", "Send 'Hello MQTT' to 'test/connectivity'"),
                TestStep("Verify message received", "Confirm message was received"),
                TestStep("Disconnect", "Clean disconnect from broker")
            )
        ),
        TestSequence(
            name = "QoS Level Testing",
            description = "Test different Quality of Service levels",
            steps = listOf(
                TestStep("Connect to broker", "Establish connection"),
                TestStep("Test QoS 0", "Send message with QoS 0 (At most once)"),
                TestStep("Test QoS 1", "Send message with QoS 1 (At least once)"),
                TestStep("Test QoS 2", "Send message with QoS 2 (Exactly once)"),
                TestStep("Compare delivery", "Verify delivery behavior differences")
            )
        ),
        TestSequence(
            name = "Wildcard Subscription Test",
            description = "Test wildcard topic subscriptions",
            steps = listOf(
                TestStep("Connect to broker", "Establish connection"),
                TestStep("Subscribe to wildcard", "Subscribe to 'test/+/data'"),
                TestStep("Publish to matching topics", "Send messages to various matching topics"),
                TestStep("Verify all received", "Confirm all matching messages received"),
                TestStep("Test non-matching", "Send to non-matching topic and verify not received")
            )
        ),
        TestSequence(
            name = "Retained Message Test",
            description = "Test retained message functionality",
            steps = listOf(
                TestStep("Connect to broker", "Establish connection"),
                TestStep("Publish retained message", "Send retained message to 'test/retained'"),
                TestStep("Disconnect", "Disconnect from broker"),
                TestStep("Reconnect and subscribe", "Reconnect and subscribe to 'test/retained'"),
                TestStep("Verify retained received", "Confirm retained message was received immediately")
            )
        ),
        TestSequence(
            name = "Load Testing",
            description = "Test broker performance under load",
            steps = listOf(
                TestStep("Connect to broker", "Establish connection"),
                TestStep("Subscribe to load topic", "Subscribe to 'test/load'"),
                TestStep("Send burst messages", "Send 100 messages rapidly"),
                TestStep("Monitor performance", "Check message delivery rate and latency"),
                TestStep("Verify all received", "Confirm all messages were delivered")
            )
        )
    )
}

/**
 * Represents a test message with topic and payload
 */
data class TestMessage(
    val name: String,
    val topic: String,
    val payload: String,
    val description: String
)

/**
 * Represents a topic pattern for subscription testing
 */
data class TestTopic(
    val pattern: String,
    val description: String,
    val examples: List<String>
)

/**
 * Represents an automated test sequence
 */
data class TestSequence(
    val name: String,
    val description: String,
    val steps: List<TestStep>
)

/**
 * Represents a single step in a test sequence
 */
data class TestStep(
    val name: String,
    val description: String
)
