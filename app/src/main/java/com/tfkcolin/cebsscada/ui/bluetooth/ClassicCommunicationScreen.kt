package com.tfkcolin.cebsscada.ui.bluetooth

import android.bluetooth.BluetoothDevice
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.bluetooth.BluetoothViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ClassicCommunicationScreen(
    device: BluetoothDevice,
    messages: List<String>,
    viewModel: BluetoothViewModel,
    onDisconnect: () -> Unit,
    onClearMessages: () -> Unit
) {
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    var dataFormat by remember { mutableStateOf("Text") } // Text or Hex

    Column(modifier = Modifier.fillMaxSize()) {
        // Device Header
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "🔗 ${device.name ?: "HC-05/HC-06"}",
                        style = MaterialTheme.typography.headlineSmall,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Text(
                        text = device.address,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                    )
                    Text(
                        text = "Classic Bluetooth (SPP)",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                    )
                }

                Row {
                    TextButton(onClick = { viewModel.queryDeviceInfo() }) {
                        Text("Device Info", color = MaterialTheme.colorScheme.onPrimaryContainer)
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    Button(
                        onClick = onDisconnect,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Text("Disconnect")
                    }
                }
            }
        }

        // Tab Layout for different communication modes
        TabRow(selectedTabIndex = selectedTabIndex, modifier = Modifier.fillMaxWidth()) {
            Tab(
                selected = selectedTabIndex == 0,
                onClick = { selectedTabIndex = 0 },
                text = { Text("SCADA Commands") }
            )
            Tab(
                selected = selectedTabIndex == 1,
                onClick = { selectedTabIndex = 1 },
                text = { Text("AT Commands") }
            )
            Tab(
                selected = selectedTabIndex == 2,
                onClick = { selectedTabIndex = 2 },
                text = { Text("Data Monitor") }
            )
        }

        // Tab Content
        when (selectedTabIndex) {
            0 -> SCADACommandsTab(viewModel)
            1 -> ATCommandsTab(viewModel)
            2 -> DataMonitorTab(messages, onClearMessages, dataFormat, { dataFormat = it })
        }
    }
}

@Composable
fun SCADACommandsTab(viewModel: BluetoothViewModel) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "SCADA Control Commands",
                style = MaterialTheme.typography.titleMedium
            )
        }

        // Quick Command Buttons
        item {
            Text("Quick Commands", style = MaterialTheme.typography.titleSmall)
            LazyRow(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                items(listOf(
                    Triple("START", "Start monitoring", { viewModel.sendSCADACommand("START") }),
                    Triple("STOP", "Stop monitoring", { viewModel.sendSCADACommand("STOP") }),
                    Triple("STATUS", "Get status", { viewModel.getSystemStatus() }),
                    Triple("RESET", "Reset device", { viewModel.resetDevice() })
                )) { (command, description, action) ->
                    OutlinedButton(
                        onClick = action,
                        modifier = Modifier.height(48.dp)
                    ) {
                        Text("$command\n$description", style = MaterialTheme.typography.labelSmall)
                    }
                }
            }
        }

        // Sensor Commands
        item {
            Text("Sensor Commands", style = MaterialTheme.typography.titleSmall)
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                    OutlinedButton(
                        onClick = { viewModel.readTemperature() },
                        modifier = Modifier.weight(1f)
                    ) { Text("📊 Read Temperature") }
                    OutlinedButton(
                        onClick = { viewModel.readPressure() },
                        modifier = Modifier.weight(1f)
                    ) { Text("🔧 Read Pressure") }
                }
                Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                    OutlinedButton(
                        onClick = { viewModel.readFlowRate() },
                        modifier = Modifier.weight(1f)
                    ) { Text("🌊 Read Flow Rate") }
                    OutlinedButton(
                        onClick = { viewModel.readLevel() },
                        modifier = Modifier.weight(1f)
                    ) { Text("📏 Read Level") }
                }
            }
        }

        // Control Commands
        item {
            Text("Control Commands", style = MaterialTheme.typography.titleSmall)
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                    Button(
                        onClick = { viewModel.openValve() },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary
                        )
                    ) { Text("🔓 Open Valve") }
                    Button(
                        onClick = { viewModel.closeValve() },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.secondary
                        )
                    ) { Text("🔒 Close Valve") }
                }
                Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                    Button(
                        onClick = { viewModel.startPump() },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.tertiary
                        )
                    ) { Text("⚡ Pump ON") }
                    Button(
                        onClick = { viewModel.stopPump() },
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error
                        )
                    ) { Text("⏹️ Pump OFF") }
                }
            }
        }

        // Custom Command Input
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text("Custom SCADA Command", style = MaterialTheme.typography.titleSmall)
                    Spacer(modifier = Modifier.height(8.dp))
                    var customCommand by remember { mutableStateOf("") }
                    var sendAsRaw by remember { mutableStateOf(false) }

                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Checkbox(
                            checked = sendAsRaw,
                            onCheckedChange = { sendAsRaw = it }
                        )
                        Text("Send as raw (no SCADA formatting)", style = MaterialTheme.typography.bodySmall)
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    OutlinedTextField(
                        value = customCommand,
                        onValueChange = { customCommand = it },
                        label = { Text(if (sendAsRaw) "Enter raw command" else "Enter SCADA command") },
                        placeholder = { Text(if (sendAsRaw) "e.g., HELLO" else "e.g., START_MONITORING") },
                        modifier = Modifier.fillMaxWidth(),
                        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Send),
                        keyboardActions = KeyboardActions(
                            onSend = {
                                if (customCommand.isNotBlank()) {
                                    if (sendAsRaw) {
                                        viewModel.sendRawCommand(customCommand)
                                    } else {
                                        viewModel.sendSCADACommand(customCommand)
                                    }
                                    customCommand = ""
                                }
                            }
                        )
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = if (sendAsRaw) "Will send: \"$customCommand\"" else "Will send: \"*$customCommand#\"",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                        Button(
                            onClick = {
                                if (customCommand.isNotBlank()) {
                                    if (sendAsRaw) {
                                        viewModel.sendRawCommand(customCommand)
                                    } else {
                                        viewModel.sendSCADACommand(customCommand)
                                    }
                                    customCommand = ""
                                }
                            },
                            enabled = customCommand.isNotBlank()
                        ) {
                            Text("Send ${if (sendAsRaw) "Raw" else "SCADA"}")
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ATCommandsTab(viewModel: BluetoothViewModel) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "HC-05/HC-06 AT Commands",
                style = MaterialTheme.typography.titleMedium
            )
            Text(
                text = "Configure your Bluetooth module",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }

        // Device Information Commands
        item {
            Text("Device Information", style = MaterialTheme.typography.titleSmall)
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                    OutlinedButton(
                        onClick = { viewModel.sendATCommand("VERSION") },
                        modifier = Modifier.weight(1f)
                    ) { Text("Version") }
                    OutlinedButton(
                        onClick = { viewModel.sendATCommand("NAME") },
                        modifier = Modifier.weight(1f)
                    ) { Text("Name") }
                }
                Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                    OutlinedButton(
                        onClick = { viewModel.sendATCommand("ADDR") },
                        modifier = Modifier.weight(1f)
                    ) { Text("Address") }
                    OutlinedButton(
                        onClick = { viewModel.sendATCommand("UART") },
                        modifier = Modifier.weight(1f)
                    ) { Text("UART Config") }
                }
            }
        }

        // Configuration Commands
        item {
            Text("Configuration", style = MaterialTheme.typography.titleSmall)
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                    OutlinedButton(
                        onClick = { viewModel.sendATCommand("ROLE") },
                        modifier = Modifier.weight(1f)
                    ) { Text("Role") }
                    OutlinedButton(
                        onClick = { viewModel.sendATCommand("PSWD") },
                        modifier = Modifier.weight(1f)
                    ) { Text("Password") }
                }
                Row(horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                    OutlinedButton(
                        onClick = { viewModel.sendATCommand("CMODE") },
                        modifier = Modifier.weight(1f)
                    ) { Text("Connection Mode") }
                    OutlinedButton(
                        onClick = { viewModel.sendATCommand("STATE") },
                        modifier = Modifier.weight(1f)
                    ) { Text("Connection State") }
                }
            }
        }

        // Custom AT Command Input
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text("Custom AT Command", style = MaterialTheme.typography.titleSmall)
                    Spacer(modifier = Modifier.height(8.dp))
                    var customATCommand by remember { mutableStateOf("") }
                    OutlinedTextField(
                        value = customATCommand,
                        onValueChange = { customATCommand = it },
                        label = { Text("Enter AT command (without AT+)") },
                        modifier = Modifier.fillMaxWidth(),
                        placeholder = { Text("e.g., NAME=MyDevice") },
                        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Send),
                        keyboardActions = KeyboardActions(
                            onSend = {
                                if (customATCommand.isNotBlank()) {
                                    viewModel.sendATCommand(customATCommand)
                                    customATCommand = ""
                                }
                            }
                        )
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Button(
                        onClick = {
                            if (customATCommand.isNotBlank()) {
                                viewModel.sendATCommand(customATCommand)
                                customATCommand = ""
                            }
                        },
                        modifier = Modifier.align(Alignment.End),
                        enabled = customATCommand.isNotBlank()
                    ) {
                        Text("Send AT Command")
                    }
                }
            }
        }
    }
}

@Composable
fun DataMonitorTab(
    messages: List<String>,
    onClearMessages: () -> Unit,
    dataFormat: String,
    onFormatChange: (String) -> Unit
) {
    Column(modifier = Modifier.fillMaxSize()) {
        // Data Format Selector
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text("Data Monitor", style = MaterialTheme.typography.titleMedium)

            Row {
                Text("Format:", style = MaterialTheme.typography.bodyMedium)
                Spacer(modifier = Modifier.width(8.dp))
                // Format toggle would be added here
            }

            if (messages.isNotEmpty()) {
                TextButton(onClick = onClearMessages) {
                    Text("Clear")
                }
            }
        }

        // Connection Status
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Bluetooth,
                    contentDescription = "Connected",
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("Connected - Receiving data from HC-05/HC-06")
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Messages Display
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .padding(horizontal = 16.dp, vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            if (messages.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Icon(
                            Icons.Default.Info,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f),
                            modifier = Modifier.size(48.dp)
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Waiting for data from HC-05/HC-06...\n\nSend commands from SCADA tab to receive responses",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                            textAlign = androidx.compose.ui.text.style.TextAlign.Center
                        )
                    }
                }
            } else {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    reverseLayout = true
                ) {
                    items(messages.reversed()) { message ->
                        val isIncoming = message.startsWith("←")
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = if (isIncoming)
                                    MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                                else
                                    MaterialTheme.colorScheme.secondary.copy(alpha = 0.1f)
                            )
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(12.dp),
                                verticalAlignment = Alignment.Top
                            ) {
                                Text(
                                    text = if (isIncoming) "📥" else "📤",
                                    style = MaterialTheme.typography.bodyLarge
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = message,
                                    style = MaterialTheme.typography.bodyMedium,
                                    modifier = Modifier.weight(1f)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MessageInput(
    onSendMessage: (String) -> Unit
) {
    var message by remember { mutableStateOf("") }
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        OutlinedTextField(
            value = message,
            onValueChange = { message = it },
            label = { Text("Message") },
            modifier = Modifier.weight(1f),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Send),
            keyboardActions = KeyboardActions(
                onSend = {
                    if (message.isNotBlank()) {
                        onSendMessage(message)
                        message = ""
                    }
                }
            )
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        IconButton(
            onClick = {
                if (message.isNotBlank()) {
                    onSendMessage(message)
                    message = ""
                }
            },
            enabled = message.isNotBlank()
        ) {
            Icon(
                imageVector = Icons.Default.Send,
                contentDescription = "Send Message"
            )
        }
    }
}
