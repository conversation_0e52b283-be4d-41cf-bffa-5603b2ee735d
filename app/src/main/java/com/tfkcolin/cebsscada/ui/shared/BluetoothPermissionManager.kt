package com.tfkcolin.cebsscada.ui.shared

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Bluetooth
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Shared Bluetooth permission manager for both Classical Bluetooth and BLE
 * Handles permission requests and status tracking across the app
 */
@Singleton
class BluetoothPermissionManager @Inject constructor(
    private val context: Context
) {
    private val _permissionState = MutableStateFlow(BluetoothPermissionState.UNKNOWN)
    val permissionState: StateFlow<BluetoothPermissionState> = _permissionState.asStateFlow()

    private val _hasAllPermissions = MutableStateFlow(false)
    val hasAllPermissions: StateFlow<Boolean> = _hasAllPermissions.asStateFlow()

    /**
     * Get required Bluetooth permissions based on Android version
     */
    fun getRequiredPermissions(): List<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            listOf(
                Manifest.permission.BLUETOOTH_SCAN,
                Manifest.permission.BLUETOOTH_CONNECT,
                Manifest.permission.BLUETOOTH_ADVERTISE,
                Manifest.permission.ACCESS_FINE_LOCATION // Still required for BLE scanning
            )
        } else {
            listOf(
                Manifest.permission.BLUETOOTH,
                Manifest.permission.BLUETOOTH_ADMIN,
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            )
        }
    }

    /**
     * Check if all required permissions are granted
     */
    fun checkPermissions(): Boolean {
        val requiredPermissions = getRequiredPermissions()
        val allGranted = requiredPermissions.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }

        _hasAllPermissions.value = allGranted
        _permissionState.value = if (allGranted) {
            BluetoothPermissionState.GRANTED
        } else {
            BluetoothPermissionState.DENIED
        }

        return allGranted
    }

    /**
     * Get missing permissions
     */
    fun getMissingPermissions(): List<String> {
        val requiredPermissions = getRequiredPermissions()
        return requiredPermissions.filter { permission ->
            ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * Update permission state after request result
     */
    fun updatePermissionState(grantedPermissions: Map<String, Boolean>) {
        val requiredPermissions = getRequiredPermissions()
        val allGranted = requiredPermissions.all { permission ->
            grantedPermissions[permission] == true
        }

        _hasAllPermissions.value = allGranted
        _permissionState.value = if (allGranted) {
            BluetoothPermissionState.GRANTED
        } else {
            BluetoothPermissionState.DENIED
        }
    }

    /**
     * Get permission explanation text
     */
    fun getPermissionExplanation(): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            "This app needs Bluetooth and Location permissions to discover and connect with devices. " +
            "On Android 12+, BLUETOOTH_SCAN, BLUETOOTH_CONNECT, and BLUETOOTH_ADVERTISE permissions are required. " +
            "Location permission is still needed for BLE device discovery."
        } else {
            "This app needs Bluetooth and Location permissions to discover and connect with devices. " +
            "Location permission is required by Android for Bluetooth scanning (location data is not used)."
        }
    }
}

/**
 * Bluetooth permission states
 */
enum class BluetoothPermissionState {
    UNKNOWN,
    GRANTED,
    DENIED,
    REQUESTING
}

/**
 * Composable for handling Bluetooth permissions with UI
 */
@Composable
fun BluetoothPermissionHandler(
    permissionManager: BluetoothPermissionManager,
    onPermissionsGranted: () -> Unit,
    onPermissionsDenied: () -> Unit
) {
    val context = LocalContext.current
    val permissionState by permissionManager.permissionState.collectAsState()
    val hasAllPermissions by permissionManager.hasAllPermissions.collectAsState()

    var showRationale by remember { mutableStateOf(false) }

    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        permissionManager.updatePermissionState(permissions)
        
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            onPermissionsGranted()
        } else {
            showRationale = true
        }
    }

    LaunchedEffect(Unit) {
        if (permissionManager.checkPermissions()) {
            onPermissionsGranted()
        } else {
            val requiredPermissions = permissionManager.getRequiredPermissions()
            permissionLauncher.launch(requiredPermissions.toTypedArray())
        }
    }

    if (showRationale) {
        BluetoothPermissionRationaleDialog(
            permissionManager = permissionManager,
            onRetry = {
                showRationale = false
                val requiredPermissions = permissionManager.getRequiredPermissions()
                permissionLauncher.launch(requiredPermissions.toTypedArray())
            },
            onDismiss = {
                showRationale = false
                onPermissionsDenied()
            }
        )
    }
}

/**
 * Permission rationale dialog
 */
@Composable
fun BluetoothPermissionRationaleDialog(
    permissionManager: BluetoothPermissionManager,
    onRetry: () -> Unit,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    val missingPermissions = permissionManager.getMissingPermissions()

    AlertDialog(
        onDismissRequest = onDismiss,
        icon = {
            Icon(
                Icons.Default.Warning,
                contentDescription = "Permission Required",
                tint = MaterialTheme.colorScheme.error
            )
        },
        title = {
            Text(
                text = "Bluetooth Permissions Required",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text(
                    text = permissionManager.getPermissionExplanation(),
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "Missing permissions:",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                
                missingPermissions.forEach { permission ->
                    Text(
                        text = "• ${getPermissionDisplayName(permission)}",
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(start = 8.dp, top = 2.dp)
                    )
                }
            }
        },
        confirmButton = {
            Button(onClick = onRetry) {
                Text("Grant Permissions")
            }
        },
        dismissButton = {
            OutlinedButton(
                onClick = {
                    val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                        data = Uri.fromParts("package", context.packageName, null)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    context.startActivity(intent)
                }
            ) {
                Text("Open Settings")
            }
        }
    )
}

/**
 * Permission denied screen
 */
@Composable
fun BluetoothPermissionDeniedScreen(
    permissionManager: BluetoothPermissionManager,
    onRetry: () -> Unit
) {
    val context = LocalContext.current

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            Icons.Default.Bluetooth,
            contentDescription = "Bluetooth",
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "Bluetooth Permissions Required",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = permissionManager.getPermissionExplanation(),
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(24.dp))

        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Button(onClick = onRetry) {
                Text("Retry")
            }

            OutlinedButton(
                onClick = {
                    val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                        data = Uri.fromParts("package", context.packageName, null)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    context.startActivity(intent)
                }
            ) {
                Text("Open Settings")
            }
        }
    }
}

/**
 * Get display name for permission
 */
private fun getPermissionDisplayName(permission: String): String {
    return when (permission) {
        Manifest.permission.BLUETOOTH -> "Bluetooth"
        Manifest.permission.BLUETOOTH_ADMIN -> "Bluetooth Admin"
        Manifest.permission.BLUETOOTH_SCAN -> "Bluetooth Scan"
        Manifest.permission.BLUETOOTH_CONNECT -> "Bluetooth Connect"
        Manifest.permission.BLUETOOTH_ADVERTISE -> "Bluetooth Advertise"
        Manifest.permission.ACCESS_FINE_LOCATION -> "Fine Location"
        Manifest.permission.ACCESS_COARSE_LOCATION -> "Coarse Location"
        else -> permission.substringAfterLast(".")
    }
}
