package com.tfkcolin.cebsscada.ui.testing

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.communication.ui.CommunicationViewModel
import kotlinx.coroutines.delay

@Composable
fun TestScenarioCard(
    scenario: TestSequence,
    enabled: Boolean,
    onSelect: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = if (enabled) onSelect else { {} },
        enabled = enabled
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = scenario.name,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = scenario.description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "${scenario.steps.size} steps",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.primary
            )
            
            if (!enabled) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "Connect to broker to run this scenario",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

@Composable
fun TestScenarioRunner(
    scenario: TestSequence,
    currentStepIndex: Int,
    isRunning: Boolean,
    onStepComplete: () -> Unit,
    onStart: () -> Unit,
    onStop: () -> Unit,
    onBack: () -> Unit,
    viewModel: CommunicationViewModel
) {
    var stepResults by remember { mutableStateOf<Map<Int, Boolean>>(emptyMap()) }
    
    Column {
        // Header with scenario info
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = scenario.name,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = scenario.description,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                        )
                    }
                    
                    IconButton(onClick = onBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Progress indicator
                LinearProgressIndicator(
                    progress = { (currentStepIndex + 1).toFloat() / scenario.steps.size },
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "Step ${currentStepIndex + 1} of ${scenario.steps.size}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Control buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            if (!isRunning) {
                Button(
                    onClick = onStart,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.PlayArrow, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Start Test")
                }
            } else {
                Button(
                    onClick = onStop,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(Icons.Default.Stop, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Stop Test")
                }
            }
            
            OutlinedButton(
                onClick = {
                    stepResults = emptyMap()
                    onStop()
                },
                modifier = Modifier.weight(1f)
            ) {
                Icon(Icons.Default.Refresh, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Reset")
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Steps list
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            itemsIndexed(scenario.steps) { index, step ->
                TestStepCard(
                    step = step,
                    stepIndex = index,
                    isCurrentStep = index == currentStepIndex,
                    isCompleted = stepResults[index] == true,
                    isFailed = stepResults[index] == false,
                    isRunning = isRunning && index == currentStepIndex
                )
            }
        }
    }
    
    // Auto-execute steps when running
    LaunchedEffect(isRunning, currentStepIndex) {
        if (isRunning && currentStepIndex < scenario.steps.size) {
            delay(1000) // Simulate step execution time
            
            // Simulate step execution based on step name
            val success = executeTestStep(scenario.steps[currentStepIndex], viewModel)
            stepResults = stepResults + (currentStepIndex to success)
            
            delay(500) // Brief pause to show result
            onStepComplete()
        }
    }
}

@Composable
fun TestStepCard(
    step: TestStep,
    stepIndex: Int,
    isCurrentStep: Boolean,
    isCompleted: Boolean,
    isFailed: Boolean,
    isRunning: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when {
                isCurrentStep && isRunning -> MaterialTheme.colorScheme.primaryContainer
                isCompleted -> MaterialTheme.colorScheme.secondaryContainer
                isFailed -> MaterialTheme.colorScheme.errorContainer
                else -> MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Step indicator
            Box(
                modifier = Modifier.size(32.dp),
                contentAlignment = Alignment.Center
            ) {
                when {
                    isRunning && isCurrentStep -> {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp
                        )
                    }
                    isCompleted -> {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = "Completed",
                            tint = Color.Green,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                    isFailed -> {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = "Failed",
                            tint = Color.Red,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                    else -> {
                        Surface(
                            modifier = Modifier.size(24.dp),
                            shape = MaterialTheme.shapes.small,
                            color = if (isCurrentStep) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.outline
                        ) {
                            Box(
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "${stepIndex + 1}",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = if (isCurrentStep) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurface
                                )
                            }
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // Step content
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = step.name,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = if (isCurrentStep) FontWeight.Bold else FontWeight.Normal
                )
                Text(
                    text = step.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
        }
    }
}

/**
 * Simulate test step execution
 */
private suspend fun executeTestStep(step: TestStep, viewModel: CommunicationViewModel): Boolean {
    return when {
        step.name.contains("Connect", ignoreCase = true) -> {
            // Connection steps usually succeed if we're already connected
            true
        }
        step.name.contains("Subscribe", ignoreCase = true) -> {
            // Simulate subscription
            viewModel.subscribe("test/scenario")
            true
        }
        step.name.contains("Publish", ignoreCase = true) -> {
            // Simulate message publishing
            viewModel.sendText("Test scenario message", "test/scenario")
            true
        }
        step.name.contains("Disconnect", ignoreCase = true) -> {
            // Simulate disconnection
            true
        }
        else -> {
            // Default: simulate success with 90% probability
            (0..9).random() < 9
        }
    }
}
