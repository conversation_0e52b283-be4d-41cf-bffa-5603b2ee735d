package com.tfkcolin.cebsscada.ui.bluetooth

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Bluetooth
import androidx.compose.material.icons.filled.BluetoothConnected
import androidx.compose.material.icons.filled.BluetoothDisabled
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.bluetooth.BluetoothDeviceWrapper
import com.tfkcolin.cebsscada.bluetooth.DeviceType
import com.tfkcolin.cebsscada.bluetooth.ScanType

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeviceListScreen(
    devices: List<BluetoothDeviceWrapper>,
    isScanning: Boolean,
    onScanStart: (ScanType) -> Unit,
    onScanStop: () -> Unit,
    onDeviceClick: (BluetoothDeviceWrapper) -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Available Devices",
                style = MaterialTheme.typography.headlineSmall
            )
            
            Row {
                if (isScanning) {
                    Button(onClick = onScanStop) {
                        Text("Stop Scan")
                    }
                } else {
                    Row {
                        Button(onClick = { onScanStart(ScanType.BLE) }) {
                            Text("Scan BLE")
                        }
                        Spacer(modifier = Modifier.width(8.dp))
                        Button(onClick = { onScanStart(ScanType.CLASSIC) }) {
                            Text("Scan Classic")
                        }
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        if (devices.isEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = if (isScanning) "Scanning for devices..." else "No devices found",
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                items(devices) { device ->
                    DeviceItem(
                        device = device,
                        onClick = { onDeviceClick(device) }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeviceItem(
    device: BluetoothDeviceWrapper,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = device.name,
                    style = MaterialTheme.typography.titleMedium
                )
                Text(
                    text = device.address,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
            
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Icon(
                        imageVector = when (device.type) {
                            DeviceType.CLASSIC -> Icons.Default.Bluetooth
                            DeviceType.BLE -> Icons.Default.BluetoothConnected
                            DeviceType.UNKNOWN -> Icons.Default.BluetoothDisabled
                        },
                        contentDescription = "Device Type",
                        tint = MaterialTheme.colorScheme.primary
                    )
                    
                    if (device.isBonded) {
                        Spacer(modifier = Modifier.width(4.dp))
                        Icon(
                            imageVector = Icons.Default.Lock,
                            contentDescription = "Paired",
                            tint = MaterialTheme.colorScheme.secondary
                        )
                    }
                }
                
                device.rssi?.let {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "$it dBm",
                        style = MaterialTheme.typography.labelSmall
                    )
                }
            }
        }
    }
}
