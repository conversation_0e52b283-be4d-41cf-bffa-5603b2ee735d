package com.tfkcolin.cebsscada.ui.bluetooth

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.BluetoothSearching
import androidx.compose.material.icons.filled.Bluetooth
import androidx.compose.material.icons.filled.BluetoothSearching
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.cebsscada.bluetooth.BluetoothViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BluetoothScreen(viewModel: BluetoothViewModel = hiltViewModel()) {
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    val isBluetoothEnabled by viewModel.isBluetoothEnabled

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Bluetooth status bar
        BluetoothStatusBar(
            isEnabled = isBluetoothEnabled,
            state = viewModel.bluetoothState.value,
            onEnable = { viewModel.enableBluetooth() }
        )

        // Tab Row
        TabRow(
            selectedTabIndex = selectedTabIndex,
            modifier = Modifier.fillMaxWidth()
        ) {
            Tab(
                selected = selectedTabIndex == 0,
                onClick = { selectedTabIndex = 0 },
                text = { Text("Classic Bluetooth") },
                icon = { Icon(Icons.Default.Bluetooth, contentDescription = "Classic Bluetooth") }
            )
            Tab(
                selected = selectedTabIndex == 1,
                onClick = { selectedTabIndex = 1 },
                text = { Text("BLE") },
                icon = { Icon(Icons.AutoMirrored.Filled.BluetoothSearching, contentDescription = "BLE") }
            )
        }

        // Tab Content
        when (selectedTabIndex) {
            0 -> ClassicBluetoothTab(viewModel = viewModel)
            1 -> BleTab(viewModel = viewModel)
        }
    }
}
