package com.tfkcolin.cebsscada.ui.bluetooth

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.bluetooth.BluetoothState
import com.tfkcolin.cebsscada.bluetooth.BluetoothViewModel
import com.tfkcolin.cebsscada.bluetooth.DeviceType
import com.tfkcolin.cebsscada.bluetooth.ScanType

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BleTab(viewModel: BluetoothViewModel) {
    val bluetoothState by viewModel.bluetoothState
    val discoveredDevices = viewModel.discoveredDevices
    val isScanning by viewModel.isScanning
    val debugLogs = viewModel.debugLogs
    val gattServices = viewModel.gattServices

    // Filter devices to show only BLE devices
    val bleDevices = discoveredDevices.filter { it.type == DeviceType.BLE }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            when (val currentState = bluetoothState) {
                is BluetoothState.Connected -> {
                    val deviceWrapper = discoveredDevices.find { it.address == currentState.device.address }
                    if (deviceWrapper?.type == DeviceType.BLE) {
                        BleCommunicationScreen(
                            device = currentState.device,
                            services = gattServices,
                            onDisconnect = { viewModel.disconnect() },
                            onReadCharacteristic = { viewModel.readCharacteristic(it) },
                            onWriteCharacteristic = { characteristic, value -> 
                                viewModel.writeCharacteristic(characteristic, value) 
                            },
                            onEnableNotifications = { viewModel.enableNotifications(it) },
                            onDisableNotifications = { viewModel.disableNotifications(it) }
                        )
                    } else {
                        // Show device list if connected to Classic device
                        BleDeviceListSection(
                            devices = bleDevices,
                            isScanning = isScanning,
                            onScanStart = { viewModel.startScan(ScanType.BLE) },
                            onScanStop = { viewModel.stopScan() },
                            onDeviceClick = { viewModel.connect(it) }
                        )
                    }
                }
                else -> {
                    BleDeviceListSection(
                        devices = bleDevices,
                        isScanning = isScanning,
                        onScanStart = { viewModel.startScan(ScanType.BLE) },
                        onScanStop = { viewModel.stopScan() },
                        onDeviceClick = { viewModel.connect(it) }
                    )
                }
            }
        }

        // Debug logs section
        item {
            OutlinedCard (
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "Debug Logs",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Button(
                            onClick = { viewModel.clearDebugLogs() },
                            modifier = Modifier.height(32.dp)
                        ) {
                            Text("Clear", style = MaterialTheme.typography.bodySmall)
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    if (debugLogs.isEmpty()) {
                        Text(
                            text = "No debug logs yet",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    } else {
                        // Show only the last 5 logs to save space
                        val recentLogs = debugLogs.takeLast(5)
                        recentLogs.forEach { log ->
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 2.dp),
                                elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
                            ) {
                                Text(
                                    text = log,
                                    style = MaterialTheme.typography.bodySmall,
                                    modifier = Modifier.padding(8.dp)
                                )
                            }
                        }
                        if (debugLogs.size > 5) {
                            Text(
                                text = "... and ${debugLogs.size - 5} more logs",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                                modifier = Modifier.padding(top = 4.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun BleDeviceListSection(
    devices: List<com.tfkcolin.cebsscada.bluetooth.BluetoothDeviceWrapper>,
    isScanning: Boolean,
    onScanStart: () -> Unit,
    onScanStop: () -> Unit,
    onDeviceClick: (com.tfkcolin.cebsscada.bluetooth.BluetoothDeviceWrapper) -> Unit
) {
    Box(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "BLE Devices",
                    style = MaterialTheme.typography.titleMedium
                )
                
                if (isScanning) {
                    Button(
                        onClick = onScanStop,
                        modifier = Modifier.height(32.dp)
                    ) {
                        Text("Stop Scan", style = MaterialTheme.typography.labelSmall)
                    }
                } else {
                    Button(
                        onClick = onScanStart,
                        modifier = Modifier.height(32.dp)
                    ) {
                        Text("Scan BLE", style = MaterialTheme.typography.labelSmall)
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (devices.isEmpty()) {
                Text(
                    text = if (isScanning) "Scanning for BLE devices..." else "No BLE devices found. Tap 'Scan BLE' to discover devices.",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            } else {
                devices.forEach { device ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        onClick = { onDeviceClick(device) },
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(12.dp)
                        ) {
                            Text(
                                text = device.name ?: "Unknown Device",
                                style = MaterialTheme.typography.titleSmall
                            )
                            Text(
                                text = device.address,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                            )
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = "BLE Device",
                                    style = MaterialTheme.typography.labelSmall,
                                    color = MaterialTheme.colorScheme.primary
                                )
                                if (device.rssi != null) {
                                    Text(
                                        text = "${device.rssi} dBm",
                                        style = MaterialTheme.typography.labelSmall,
                                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
