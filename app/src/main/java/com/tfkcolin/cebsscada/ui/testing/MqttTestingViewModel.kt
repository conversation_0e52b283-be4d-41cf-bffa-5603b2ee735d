package com.tfkcolin.cebsscada.ui.testing

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.cebsscada.communication.CommunicationManager
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.communication.QoSLevel
import com.tfkcolin.cebsscada.communication.models.CommunicationDevice
import com.tfkcolin.cebsscada.communication.models.CommunicationMessage
import com.tfkcolin.cebsscada.communication.models.TopicSubscription
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Dedicated ViewModel for MQTT testing functionality
 */
@HiltViewModel
class MqttTestingViewModel @Inject constructor(
    private val communicationManager: CommunicationManager
) : ViewModel() {

    // UI State
    private val _selectedProtocol = MutableStateFlow(CommunicationProtocol.MQTT)
    val selectedProtocol: StateFlow<CommunicationProtocol> = _selectedProtocol.asStateFlow()

    private val _messages = MutableStateFlow<List<CommunicationMessage>>(emptyList())
    val messages: StateFlow<List<CommunicationMessage>> = _messages.asStateFlow()

    private val _isScanning = MutableStateFlow(false)
    val isScanning: StateFlow<Boolean> = _isScanning.asStateFlow()

    // Manual device configuration for MQTT brokers
    private val _manualDevices = MutableStateFlow<Map<CommunicationProtocol, List<CommunicationDevice>>>(emptyMap())
    val manualDevices: StateFlow<Map<CommunicationProtocol, List<CommunicationDevice>>> = _manualDevices.asStateFlow()

    // Derived state flows
    val supportedProtocols = MutableStateFlow(listOf(CommunicationProtocol.MQTT))
    val allDevices = communicationManager.allDevices
    val connectionStates = communicationManager.connectionStates
    val connectedDevices = communicationManager.connectedDevices

    // Protocol-specific state
    val currentDevices = combine(allDevices, manualDevices, selectedProtocol) { devices, manual, protocol ->
        val discoveredDevices = devices.filter { it.protocol == protocol }
        val manualDevicesForProtocol = manual[protocol] ?: emptyList()
        discoveredDevices + manualDevicesForProtocol
    }

    val currentConnectionState = combine(connectionStates, selectedProtocol) { states, protocol ->
        states[protocol] ?: ConnectionState.DISCONNECTED
    }

    val currentConnectedDevice = combine(connectedDevices, selectedProtocol) { devices, protocol ->
        devices[protocol]
    }

    init {
        // Collect all messages from communication manager
        viewModelScope.launch {
            communicationManager.allMessages.collect { message ->
                val currentMessages = _messages.value.toMutableList()
                currentMessages.add(message)
                // Keep only last 1000 messages to prevent memory issues
                if (currentMessages.size > 1000) {
                    currentMessages.removeAt(0)
                }
                _messages.value = currentMessages
            }
        }

        // Ensure MQTT protocol is always selected for this screen
        _selectedProtocol.value = CommunicationProtocol.MQTT
    }

    /**
     * Start device discovery for MQTT brokers
     */
    fun startDiscovery() {
        viewModelScope.launch {
            _isScanning.value = true
            try {
                communicationManager.startDiscovery(_selectedProtocol.value)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }

    /**
     * Stop device discovery
     */
    fun stopDiscovery() {
        viewModelScope.launch {
            try {
                communicationManager.stopDiscovery(_selectedProtocol.value)
            } finally {
                _isScanning.value = false
            }
        }
    }

    /**
     * Connect to an MQTT broker
     */
    fun connect(device: CommunicationDevice, options: Map<String, Any> = emptyMap()) {
        viewModelScope.launch {
            communicationManager.connect(device, options)
        }
    }

    /**
     * Disconnect from the current MQTT broker
     */
    fun disconnect() {
        viewModelScope.launch {
            communicationManager.disconnect(_selectedProtocol.value)
        }
    }

    /**
     * Send text message to MQTT topic
     */
    fun sendText(text: String, topic: String? = null) {
        viewModelScope.launch {
            communicationManager.sendText(_selectedProtocol.value, text, topic)
        }
    }

    /**
     * Subscribe to an MQTT topic
     */
    fun subscribe(topic: String, qosLevel: QoSLevel = QoSLevel.AT_LEAST_ONCE) {
        viewModelScope.launch {
            val subscription = TopicSubscription(topic, qosLevel)
            communicationManager.subscribe(_selectedProtocol.value, subscription)
        }
    }

    /**
     * Unsubscribe from an MQTT topic
     */
    fun unsubscribe(topic: String) {
        viewModelScope.launch {
            communicationManager.unsubscribe(_selectedProtocol.value, topic)
        }
    }

    /**
     * Get subscriptions for MQTT
     */
    fun getSubscriptions() = communicationManager.getSubscriptions(_selectedProtocol.value)

    /**
     * Clear all messages
     */
    fun clearMessages() {
        _messages.value = emptyList()
    }

    /**
     * Get connection statistics
     */
    fun getConnectionStats(): MqttConnectionStats {
        val messages = _messages.value
        return MqttConnectionStats(
            messagesSent = messages.count { it.isOutgoing },
            messagesReceived = messages.count { !it.isOutgoing },
            totalMessages = messages.size,
            subscriptions = getSubscriptions().size
        )
    }

    /**
     * Add a manually configured MQTT broker
     */
    fun addManualDevice(device: CommunicationDevice) {
        val currentManual = _manualDevices.value.toMutableMap()
        val devicesForProtocol = currentManual[device.protocol]?.toMutableList() ?: mutableListOf()

        // Check if device already exists
        if (!devicesForProtocol.any { it.id == device.id }) {
            devicesForProtocol.add(device)
            currentManual[device.protocol] = devicesForProtocol
            _manualDevices.value = currentManual
        }
    }

    /**
     * Remove a manually configured MQTT broker
     */
    fun removeManualDevice(device: CommunicationDevice) {
        val currentManual = _manualDevices.value.toMutableMap()
        val devicesForProtocol = currentManual[device.protocol]?.toMutableList() ?: return

        devicesForProtocol.removeAll { it.id == device.id }
        if (devicesForProtocol.isEmpty()) {
            currentManual.remove(device.protocol)
        } else {
            currentManual[device.protocol] = devicesForProtocol
        }
        _manualDevices.value = currentManual
    }

    override fun onCleared() {
        super.onCleared()
        viewModelScope.launch {
            communicationManager.cleanup()
        }
    }
}

/**
 * MQTT connection statistics data class
 */
data class MqttConnectionStats(
    val messagesSent: Int = 0,
    val messagesReceived: Int = 0,
    val totalMessages: Int = 0,
    val subscriptions: Int = 0
)