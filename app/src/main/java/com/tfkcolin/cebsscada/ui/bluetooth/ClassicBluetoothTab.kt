package com.tfkcolin.cebsscada.ui.bluetooth

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.BugReport
import androidx.compose.material.icons.filled.CleaningServices
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.bluetooth.BluetoothState
import com.tfkcolin.cebsscada.bluetooth.BluetoothViewModel
import com.tfkcolin.cebsscada.bluetooth.DeviceType
import com.tfkcolin.cebsscada.bluetooth.ScanType

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ClassicBluetoothTab(viewModel: BluetoothViewModel) {
    val bluetoothState by viewModel.bluetoothState
    val discoveredDevices = viewModel.discoveredDevices
    val messages = viewModel.messages
    val isScanning by viewModel.isScanning
    val debugLogs = viewModel.debugLogs

    // Filter devices to show only Classic Bluetooth devices
    val classicDevices = discoveredDevices.filter { it.type == DeviceType.CLASSIC }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            when (val currentState = bluetoothState) {
                is BluetoothState.Connected -> {
                    val deviceWrapper = discoveredDevices.find { it.address == currentState.device.address }
                    if (deviceWrapper?.type == DeviceType.CLASSIC) {
                        ClassicCommunicationScreen(
                            device = currentState.device,
                            messages = messages,
                            viewModel = viewModel,
                            onDisconnect = { viewModel.disconnect() },
                            onClearMessages = { viewModel.clearMessages() }
                        )
                    } else {
                        // Show device list if connected to BLE device
                        ClassicDeviceListSection(
                            devices = classicDevices,
                            isScanning = isScanning,
                            onScanStart = { viewModel.startScan(ScanType.CLASSIC) },
                            onScanStop = { viewModel.stopScan() },
                            onDeviceClick = { viewModel.connect(it) }
                        )
                    }
                }
                else -> {
                    ClassicDeviceListSection(
                        devices = classicDevices,
                        isScanning = isScanning,
                        onScanStart = { viewModel.startScan(ScanType.CLASSIC) },
                        onScanStop = { viewModel.stopScan() },
                        onDeviceClick = { viewModel.connect(it) }
                    )
                }
            }
        }

        // Debug logs section
        item {
            OutlinedCard (
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                shape = RectangleShape
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "Debug Logs",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Row {
                            IconButton (
                                onClick = { viewModel.diagnoseBluetoothState() },
                                modifier = Modifier.height(32.dp).padding(horizontal = 8.dp),
                            ) {
                                Icon(imageVector = Icons.Default.BugReport, contentDescription = null)
                            }
                            Spacer(modifier = Modifier.width(8.dp))
                            Button(
                                onClick = { viewModel.clearDebugLogs() },
                                modifier = Modifier.height(32.dp)
                            ) {
                                Icon(imageVector = Icons.Default.CleaningServices, contentDescription = null)
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    if (debugLogs.isEmpty()) {
                        Text(
                            text = "No debug logs yet - try scanning or run diagnostics",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    } else {
                        // Scrollable log display with newest logs at top
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(max = 300.dp), // Limit height to prevent taking too much space
                            reverseLayout = true // Newest logs appear at top
                        ) {
                            items(debugLogs.reversed()) { log -> // Reverse to show newest first
                                OutlinedCard (
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 2.dp),
                                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
                                    colors = CardDefaults.cardColors(
                                        containerColor = when {
                                            log.contains("ERROR") || log.contains("FAILURE") || log.contains("❌") -> MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f)
                                            log.contains("SUCCESS") || log.contains("✅") -> MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                                            log.contains("WARNING") || log.contains("⚠️") -> MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
                                            log.contains("RECEIVED") -> MaterialTheme.colorScheme.tertiaryContainer.copy(alpha = 0.2f)
                                            else -> MaterialTheme.colorScheme.surface
                                        }
                                    )
                                ) {
                                    Text(
                                        text = log,
                                        style = MaterialTheme.typography.bodySmall,
                                        modifier = Modifier.padding(8.dp),
                                        color = when {
                                            log.contains("ERROR") || log.contains("FAILURE") || log.contains("❌") -> MaterialTheme.colorScheme.onErrorContainer
                                            log.contains("SUCCESS") || log.contains("✅") -> MaterialTheme.colorScheme.onPrimaryContainer
                                            else -> MaterialTheme.colorScheme.onSurface
                                        }
                                    )
                                }
                            }
                        }

                        // Show total count if there are many logs
                        if (debugLogs.size > 10) {
                            Text(
                                text = "Total logs: ${debugLogs.size} (newest at top)",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                                modifier = Modifier.padding(top = 8.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ClassicDeviceListSection(
    devices: List<com.tfkcolin.cebsscada.bluetooth.BluetoothDeviceWrapper>,
    isScanning: Boolean,
    onScanStart: () -> Unit,
    onScanStop: () -> Unit,
    onDeviceClick: (com.tfkcolin.cebsscada.bluetooth.BluetoothDeviceWrapper) -> Unit
) {
    Box(
        modifier = Modifier.fillMaxWidth(),
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "Devices",
                    style = MaterialTheme.typography.titleMedium
                )
                
                if (isScanning) {
                    Button(
                        onClick = onScanStop,
                        modifier = Modifier.height(32.dp)
                    ) {
                        Text("Stop Scan", style = MaterialTheme.typography.labelSmall)
                    }
                } else {
                    Button(
                        onClick = onScanStart,
                        modifier = Modifier.height(32.dp)
                    ) {
                        Text("Scan", style = MaterialTheme.typography.labelSmall)
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (devices.isEmpty()) {
                Text(
                    text = if (isScanning) "Scanning for Classic Bluetooth devices..." else "No Classic Bluetooth devices found. Tap 'Scan Classic' to discover devices.",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            } else {
                devices.forEach { device ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        onClick = { onDeviceClick(device) },
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(12.dp)
                        ) {
                            Text(
                                text = device.name ?: "Unknown Device",
                                style = MaterialTheme.typography.titleSmall
                            )
                            Text(
                                text = device.address,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                            )
                            Text(
                                text = "Classic Bluetooth",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                }
            }
        }
    }
}
