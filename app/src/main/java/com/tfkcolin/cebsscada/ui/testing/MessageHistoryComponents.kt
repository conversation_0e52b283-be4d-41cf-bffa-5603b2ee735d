package com.tfkcolin.cebsscada.ui.testing

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.communication.models.CommunicationMessage
import com.tfkcolin.cebsscada.communication.models.MessageDirection
import java.text.SimpleDateFormat
import java.util.*

/**
 * Enhanced message history with filtering and search capabilities
 */
@Composable
fun EnhancedMessageHistory(
    messages: List<CommunicationMessage>,
    onClearMessages: () -> Unit,
    onExportMessages: () -> Unit = {}
) {
    var searchQuery by remember { mutableStateOf("") }
    var selectedTopicFilter by remember { mutableStateOf<String?>(null) }
    var selectedDirectionFilter by remember { mutableStateOf<MessageDirection?>(null) }
    var showFilters by remember { mutableStateOf(false) }
    
    // Extract unique topics from messages
    val availableTopics = remember(messages) {
        messages.mapNotNull { it.topic }.distinct().sorted()
    }
    
    // Filter messages based on search and filters
    val filteredMessages = remember(messages, searchQuery, selectedTopicFilter, selectedDirectionFilter) {
        messages.filter { message ->
            val matchesSearch = if (searchQuery.isBlank()) true else {
                message.contentAsString.contains(searchQuery, ignoreCase = true) ||
                message.topic?.contains(searchQuery, ignoreCase = true) == true
            }
            
            val matchesTopic = selectedTopicFilter?.let { filter ->
                message.topic == filter
            } ?: true
            
            val matchesDirection = selectedDirectionFilter?.let { filter ->
                message.direction == filter
            } ?: true
            
            matchesSearch && matchesTopic && matchesDirection
        }
    }
    
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Header with controls
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Message History",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    IconButton(
                        onClick = { showFilters = !showFilters }
                    ) {
                        Icon(
                            Icons.Default.FilterList,
                            contentDescription = "Filters",
                            tint = if (showFilters || selectedTopicFilter != null || selectedDirectionFilter != null) {
                                MaterialTheme.colorScheme.primary
                            } else {
                                MaterialTheme.colorScheme.onSurface
                            }
                        )
                    }
                    
                    IconButton(onClick = onExportMessages) {
                        Icon(Icons.Default.Download, contentDescription = "Export")
                    }
                    
                    if (messages.isNotEmpty()) {
                        IconButton(onClick = onClearMessages) {
                            Icon(
                                Icons.Default.Delete,
                                contentDescription = "Clear",
                                tint = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                }
            }
            
            // Message count and filter status
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "${filteredMessages.size} of ${messages.size} messages",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
                
                if (selectedTopicFilter != null || selectedDirectionFilter != null) {
                    TextButton(
                        onClick = {
                            selectedTopicFilter = null
                            selectedDirectionFilter = null
                        }
                    ) {
                        Text("Clear Filters", style = MaterialTheme.typography.bodySmall)
                    }
                }
            }
            
            // Search and filters
            if (showFilters) {
                Spacer(modifier = Modifier.height(8.dp))
                
                OutlinedTextField(
                    value = searchQuery,
                    onValueChange = { searchQuery = it },
                    label = { Text("Search messages") },
                    placeholder = { Text("Search in content or topic...") },
                    modifier = Modifier.fillMaxWidth(),
                    leadingIcon = {
                        Icon(Icons.Default.Search, contentDescription = null)
                    },
                    trailingIcon = {
                        if (searchQuery.isNotEmpty()) {
                            IconButton(onClick = { searchQuery = "" }) {
                                Icon(Icons.Default.Clear, contentDescription = "Clear search")
                            }
                        }
                    },
                    singleLine = true
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Filter chips
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Direction filter
                    FilterChip(
                        onClick = {
                            selectedDirectionFilter = if (selectedDirectionFilter == MessageDirection.INCOMING) {
                                MessageDirection.OUTGOING
                            } else if (selectedDirectionFilter == MessageDirection.OUTGOING) {
                                null
                            } else {
                                MessageDirection.INCOMING
                            }
                        },
                        label = {
                            Text(
                                when (selectedDirectionFilter) {
                                    MessageDirection.INCOMING -> "Incoming"
                                    MessageDirection.OUTGOING -> "Outgoing"
                                    null -> "All Directions"
                                },
                                style = MaterialTheme.typography.bodySmall
                            )
                        },
                        selected = selectedDirectionFilter != null
                    )
                    
                    // Topic filter dropdown
                    if (availableTopics.isNotEmpty()) {
                        var showTopicDropdown by remember { mutableStateOf(false) }
                        
                        FilterChip(
                            onClick = { showTopicDropdown = true },
                            label = {
                                Text(
                                    selectedTopicFilter ?: "All Topics",
                                    style = MaterialTheme.typography.bodySmall
                                )
                            },
                            selected = selectedTopicFilter != null,
                            trailingIcon = {
                                Icon(
                                    Icons.Default.ArrowDropDown,
                                    contentDescription = null,
                                    modifier = Modifier.size(16.dp)
                                )
                            }
                        )
                        
                        DropdownMenu(
                            expanded = showTopicDropdown,
                            onDismissRequest = { showTopicDropdown = false }
                        ) {
                            DropdownMenuItem(
                                text = { Text("All Topics") },
                                onClick = {
                                    selectedTopicFilter = null
                                    showTopicDropdown = false
                                }
                            )
                            
                            availableTopics.forEach { topic ->
                                DropdownMenuItem(
                                    text = { Text(topic) },
                                    onClick = {
                                        selectedTopicFilter = topic
                                        showTopicDropdown = false
                                    }
                                )
                            }
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Messages list
            if (filteredMessages.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            Icons.Default.Message,
                            contentDescription = null,
                            modifier = Modifier.size(48.dp),
                            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f)
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = if (messages.isEmpty()) "No messages yet" else "No messages match filters",
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                }
            } else {
                LazyColumn(
                    modifier = Modifier.heightIn(max = 400.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    reverseLayout = true
                ) {
                    items(filteredMessages.reversed()) { message ->
                        EnhancedMessageItem(message = message)
                    }
                }
            }
        }
    }
}

@Composable
fun EnhancedMessageItem(message: CommunicationMessage) {
    val dateFormat = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault())
    val timeString = dateFormat.format(Date(message.timestamp))
    
    val isOutgoing = message.direction == MessageDirection.OUTGOING
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isOutgoing) {
                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
            } else {
                MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // Header with topic, direction, and timestamp
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Direction indicator
                    Icon(
                        imageVector = if (isOutgoing) Icons.Default.ArrowUpward else Icons.Default.ArrowDownward,
                        contentDescription = if (isOutgoing) "Outgoing" else "Incoming",
                        tint = if (isOutgoing) Color.Blue else Color.Green,
                        modifier = Modifier.size(16.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    // Topic
                    Text(
                        text = message.topic ?: "No topic",
                        style = MaterialTheme.typography.bodySmall,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                
                // Timestamp
                Text(
                    text = timeString,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // Message content
            SelectionContainer {
                Text(
                    text = message.contentAsString,
                    style = MaterialTheme.typography.bodySmall,
                    fontFamily = FontFamily.Monospace,
                    modifier = Modifier.fillMaxWidth()
                )
            }
            
            // Message metadata
            if (message.qosLevel.name != "AT_LEAST_ONCE" || message.retained || message.size > 100) {
                Spacer(modifier = Modifier.height(4.dp))
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    if (message.qosLevel.name != "AT_LEAST_ONCE") {
                        MessageMetadataChip("QoS: ${message.qosLevel.name}")
                    }
                    
                    if (message.retained) {
                        MessageMetadataChip("Retained")
                    }
                    
                    MessageMetadataChip("${message.size} bytes")
                }
            }
        }
    }
}

@Composable
fun MessageMetadataChip(text: String) {
    Surface(
        color = MaterialTheme.colorScheme.outline.copy(alpha = 0.1f),
        shape = MaterialTheme.shapes.extraSmall,
        modifier = Modifier.padding(2.dp)
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
        )
    }
}
