package com.tfkcolin.cebsscada.di

import android.bluetooth.BluetoothAdapter
import android.content.Context
import com.tfkcolin.cebsscada.bluetooth.BLEScanner
import com.tfkcolin.cebsscada.communication.CommunicationManager
import com.tfkcolin.cebsscada.communication.factory.CommunicationServiceFactoryImpl
import com.tfkcolin.cebsscada.communication.factory.DeviceScannerFactoryImpl
import com.tfkcolin.cebsscada.communication.interfaces.CommunicationServiceFactory
import com.tfkcolin.cebsscada.communication.interfaces.DeviceScannerFactory
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object CommunicationModule {

    @Provides
    @Singleton
    fun provideBluetoothAdapter(): BluetoothAdapter? {
        return BluetoothAdapter.getDefaultAdapter()
    }

    @Provides
    @Singleton
    fun provideBLEScanner(@ApplicationContext context: Context): BLEScanner {
        return BLEScanner(context)
    }

    @Provides
    @Singleton
    fun provideCommunicationServiceFactory(
        @ApplicationContext context: Context,
        bluetoothAdapter: BluetoothAdapter?
    ): CommunicationServiceFactory {
        return CommunicationServiceFactoryImpl(context, bluetoothAdapter)
    }

    @Provides
    @Singleton
    fun provideDeviceScannerFactory(
        @ApplicationContext context: Context,
        bluetoothAdapter: BluetoothAdapter?
    ): DeviceScannerFactory {
        return DeviceScannerFactoryImpl(context, bluetoothAdapter)
    }

    @Provides
    @Singleton
    fun provideCommunicationManager(
        serviceFactory: CommunicationServiceFactory,
        scannerFactory: DeviceScannerFactory
    ): CommunicationManager {
        return CommunicationManager(serviceFactory, scannerFactory)
    }
}
