package com.tfkcolin.cebsscada

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Bluetooth
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Router
import androidx.compose.material.icons.filled.Science
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.cebsscada.bluetooth.BluetoothViewModel
import com.tfkcolin.cebsscada.communication.ui.CommunicationScreen
import com.tfkcolin.cebsscada.ui.permissions.BluetoothApp
import com.tfkcolin.cebsscada.ui.permissions.BluetoothPermissionHandler
import com.tfkcolin.cebsscada.ui.permissions.PermissionDeniedScreen
import com.tfkcolin.cebsscada.ui.testing.MqttTestingScreen
import com.tfkcolin.cebsscada.ui.testing.ArchitectureValidationScreen
import com.tfkcolin.cebsscada.ui.classical.ClassicalBluetoothScreen
import com.tfkcolin.cebsscada.ui.ble.BleScreen
import com.tfkcolin.cebsscada.ui.shared.BluetoothPermissionManager
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import androidx.core.content.ContextCompat
import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import com.tfkcolin.cebsscada.ui.theme.CEBSSCADATheme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            CEBSSCADATheme {
                CommunicationApp()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommunicationApp() {
    var selectedTab by remember { mutableIntStateOf(0) }

    val tabs = listOf(
        TabItem("Classic BT", Icons.Default.Bluetooth),
        TabItem("BLE", Icons.Default.Router),
        TabItem("MQTT", Icons.Default.Science),
        TabItem("Validation", Icons.Default.CheckCircle)
    )

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("CEBS SCADA - Communication Hub") }
            )
        },
        bottomBar = {
            NavigationBar {
                tabs.forEachIndexed { index, tab ->
                    NavigationBarItem(
                        icon = { Icon(tab.icon, contentDescription = tab.title) },
                        label = { Text(tab.title) },
                        selected = selectedTab == index,
                        onClick = { selectedTab = index }
                    )
                }
            }
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            when (selectedTab) {
                0 -> {
                    val permissionManager: BluetoothPermissionManager = hiltViewModel()
                    ClassicalBluetoothScreen(permissionManager = permissionManager)
                }
                1 -> {
                    val permissionManager: BluetoothPermissionManager = hiltViewModel()
                    BleScreen(permissionManager = permissionManager)
                }
                2 -> MqttTestingScreen()
                3 -> ArchitectureValidationScreen()
            }
        }
    }
}

data class TabItem(
    val title: String,
    val icon: ImageVector
)

@Composable
fun UnifiedCommunicationApp() {
    val context = LocalContext.current
    var hasPermissions by remember { mutableStateOf(false) }
    var showPermissionDenied by remember { mutableStateOf(false) }

    val bluetoothPermissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        listOf(
            Manifest.permission.BLUETOOTH_SCAN,
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.BLUETOOTH_ADVERTISE,
            Manifest.permission.ACCESS_FINE_LOCATION
        )
    } else {
        listOf(
            Manifest.permission.BLUETOOTH,
            Manifest.permission.BLUETOOTH_ADMIN,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
        )
    }

    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.all { it.value }
        if (allGranted) {
            hasPermissions = true
        } else {
            showPermissionDenied = true
        }
    }

    LaunchedEffect(Unit) {
        val hasAllPermissions = bluetoothPermissions.all {
            ContextCompat.checkSelfPermission(context, it) == PackageManager.PERMISSION_GRANTED
        }
        if (hasAllPermissions) {
            hasPermissions = true
        } else {
            permissionLauncher.launch(bluetoothPermissions.toTypedArray())
        }
    }

    if (hasPermissions) {
        CommunicationScreen()
    } else if (showPermissionDenied) {
        PermissionDeniedScreen(
            onRetry = { showPermissionDenied = false },
            onOpenSettings = {
                val intent = android.content.Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = android.net.Uri.fromParts("package", context.packageName, null)
                    addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
            }
        )
    }
    // Loading state is implicit during permission check
}