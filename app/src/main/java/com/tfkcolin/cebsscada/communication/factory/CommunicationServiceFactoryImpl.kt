package com.tfkcolin.cebsscada.communication.factory

import android.bluetooth.BluetoothAdapter
import android.content.Context
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.bluetooth.BluetoothCommunicationService
import com.tfkcolin.cebsscada.communication.interfaces.CommunicationService
import com.tfkcolin.cebsscada.communication.interfaces.CommunicationServiceFactory
import com.tfkcolin.cebsscada.communication.mqtt.MqttCommunicationService
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Factory implementation for creating communication services
 */
@Singleton
class CommunicationServiceFactoryImpl @Inject constructor(
    private val context: Context,
    private val bluetoothAdapter: BluetoothAdapter?
) : CommunicationServiceFactory {
    
    override fun createService(protocol: CommunicationProtocol): CommunicationService? {
        return when (protocol) {
            CommunicationProtocol.BLUETOOTH_CLASSIC,
            CommunicationProtocol.BLUETOOTH_BLE -> {
                if (bluetoothAdapter != null) {
                    BluetoothCommunicationService(context, bluetoothAdapter)
                } else {
                    null
                }
            }
            CommunicationProtocol.MQTT -> {
                MqttCommunicationService(context)
            }
            CommunicationProtocol.TCP -> {
                // TODO: Implement TCP service
                null
            }
            CommunicationProtocol.UDP -> {
                // TODO: Implement UDP service
                null
            }
            CommunicationProtocol.SERIAL -> {
                // TODO: Implement Serial service
                null
            }
            CommunicationProtocol.MODBUS -> {
                // TODO: Implement Modbus service
                null
            }
            CommunicationProtocol.UNKNOWN -> null
        }
    }
    
    override fun getSupportedProtocols(): List<CommunicationProtocol> {
        val protocols = mutableListOf<CommunicationProtocol>()

        // Add Bluetooth protocols if adapter is available
        if (bluetoothAdapter != null) {
            protocols.add(CommunicationProtocol.BLUETOOTH_CLASSIC)
            protocols.add(CommunicationProtocol.BLUETOOTH_BLE)
        }

        // MQTT removed as it does not follow the same process for setting it up as Bluetooth protocols
        // protocols.add(CommunicationProtocol.MQTT)

        // TODO: Add other protocols as they are implemented
        // protocols.add(CommunicationProtocol.TCP)
        // protocols.add(CommunicationProtocol.UDP)
        // protocols.add(CommunicationProtocol.SERIAL)
        // protocols.add(CommunicationProtocol.MODBUS)

        return protocols
    }
    
    override fun isProtocolSupported(protocol: CommunicationProtocol): Boolean {
        return getSupportedProtocols().contains(protocol)
    }
}
