package com.tfkcolin.cebsscada.communication.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.models.CommunicationDevice
import com.tfkcolin.cebsscada.communication.models.MqttBroker
import com.tfkcolin.cebsscada.communication.models.TcpServer
import com.tfkcolin.cebsscada.communication.models.UdpServer

/**
 * Dialog for manually configuring communication devices
 */
@Composable
fun DeviceConfigurationDialog(
    protocol: CommunicationProtocol,
    onDismiss: () -> Unit,
    onDeviceConfigured: (CommunicationDevice) -> Unit
) {
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                Text(
                    text = "Configure ${protocol.name.replace("_", " ")} Device",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                when (protocol) {
                    CommunicationProtocol.MQTT -> {
                        MqttConfigurationForm(
                            onDismiss = onDismiss,
                            onDeviceConfigured = onDeviceConfigured
                        )
                    }
                    CommunicationProtocol.TCP -> {
                        TcpConfigurationForm(
                            onDismiss = onDismiss,
                            onDeviceConfigured = onDeviceConfigured
                        )
                    }
                    CommunicationProtocol.UDP -> {
                        UdpConfigurationForm(
                            onDismiss = onDismiss,
                            onDeviceConfigured = onDeviceConfigured
                        )
                    }
                    else -> {
                        Text("Manual configuration not available for this protocol")
                        Spacer(modifier = Modifier.height(16.dp))
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.End
                        ) {
                            TextButton(onClick = onDismiss) {
                                Text("Close")
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun MqttConfigurationForm(
    onDismiss: () -> Unit,
    onDeviceConfigured: (CommunicationDevice) -> Unit
) {
    var name by remember { mutableStateOf("MQTT Broker") }
    var address by remember { mutableStateOf("test.mosquitto.org") }
    var port by remember { mutableStateOf("1883") }
    var username by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var clientId by remember { mutableStateOf("") }
    var useTls by remember { mutableStateOf(false) }
    var passwordVisible by remember { mutableStateOf(false) }
    
    Column {
        OutlinedTextField(
            value = name,
            onValueChange = { name = it },
            label = { Text("Broker Name") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        OutlinedTextField(
            value = address,
            onValueChange = { address = it },
            label = { Text("Broker Address") },
            placeholder = { Text("mqtt.broker.com") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        OutlinedTextField(
            value = port,
            onValueChange = { port = it },
            label = { Text("Port") },
            placeholder = { Text("1883") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        OutlinedTextField(
            value = clientId,
            onValueChange = { clientId = it },
            label = { Text("Client ID (optional)") },
            placeholder = { Text("Auto-generated if empty") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        OutlinedTextField(
            value = username,
            onValueChange = { username = it },
            label = { Text("Username (optional)") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        OutlinedTextField(
            value = password,
            onValueChange = { password = it },
            label = { Text("Password (optional)") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
            trailingIcon = {
                IconButton(onClick = { passwordVisible = !passwordVisible }) {
                    Icon(
                        imageVector = if (passwordVisible) Icons.Default.Visibility else Icons.Default.VisibilityOff,
                        contentDescription = if (passwordVisible) "Hide password" else "Show password"
                    )
                }
            }
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = useTls,
                onCheckedChange = { useTls = it }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("Use TLS/SSL")
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.End)
        ) {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
            
            Button(
                onClick = {
                    val broker = MqttBroker(
                        id = "${address}:${port}",
                        name = name.ifBlank { "MQTT Broker" },
                        address = address,
                        port = port.toIntOrNull() ?: 1883,
                        username = username.ifBlank { null },
                        password = password.ifBlank { null },
                        useTls = useTls,
                        clientId = clientId.ifBlank { null }
                    )
                    onDeviceConfigured(broker)
                },
                enabled = address.isNotBlank() && port.isNotBlank()
            ) {
                Text("Add Broker")
            }
        }
    }
}

@Composable
fun TcpConfigurationForm(
    onDismiss: () -> Unit,
    onDeviceConfigured: (CommunicationDevice) -> Unit
) {
    var name by remember { mutableStateOf("TCP Server") }
    var address by remember { mutableStateOf("") }
    var port by remember { mutableStateOf("") }
    
    Column {
        OutlinedTextField(
            value = name,
            onValueChange = { name = it },
            label = { Text("Server Name") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        OutlinedTextField(
            value = address,
            onValueChange = { address = it },
            label = { Text("Server Address") },
            placeholder = { Text("*************") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        OutlinedTextField(
            value = port,
            onValueChange = { port = it },
            label = { Text("Port") },
            placeholder = { Text("8080") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.End)
        ) {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
            
            Button(
                onClick = {
                    val server = TcpServer(
                        id = "${address}:${port}",
                        name = name.ifBlank { "TCP Server" },
                        address = address,
                        port = port.toIntOrNull() ?: 8080
                    )
                    onDeviceConfigured(server)
                },
                enabled = address.isNotBlank() && port.isNotBlank()
            ) {
                Text("Add Server")
            }
        }
    }
}

@Composable
fun UdpConfigurationForm(
    onDismiss: () -> Unit,
    onDeviceConfigured: (CommunicationDevice) -> Unit
) {
    var name by remember { mutableStateOf("UDP Server") }
    var address by remember { mutableStateOf("") }
    var port by remember { mutableStateOf("") }
    
    Column {
        OutlinedTextField(
            value = name,
            onValueChange = { name = it },
            label = { Text("Server Name") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        OutlinedTextField(
            value = address,
            onValueChange = { address = it },
            label = { Text("Server Address") },
            placeholder = { Text("*************") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        OutlinedTextField(
            value = port,
            onValueChange = { port = it },
            label = { Text("Port") },
            placeholder = { Text("5000") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.End)
        ) {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
            
            Button(
                onClick = {
                    val server = UdpServer(
                        id = "${address}:${port}",
                        name = name.ifBlank { "UDP Server" },
                        address = address,
                        port = port.toIntOrNull() ?: 5000
                    )
                    onDeviceConfigured(server)
                },
                enabled = address.isNotBlank() && port.isNotBlank()
            ) {
                Text("Add Server")
            }
        }
    }
}
