package com.tfkcolin.cebsscada.communication.models

import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.DeviceType

/**
 * Base class representing a communication device that can be connected to
 */
abstract class CommunicationDevice {
    abstract val id: String
    abstract val name: String
    abstract val address: String
    abstract val protocol: CommunicationProtocol
    abstract val deviceType: DeviceType
    abstract val isAvailable: Boolean
    
    /**
     * Additional properties specific to the device type
     */
    open val properties: Map<String, Any> = emptyMap()
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is CommunicationDevice) return false
        return id == other.id && protocol == other.protocol
    }
    
    override fun hashCode(): Int {
        return id.hashCode() * 31 + protocol.hashCode()
    }
}

/**
 * Represents a Bluetooth device (Classic or BLE)
 */
data class BluetoothDevice(
    override val id: String,
    override val name: String,
    override val address: String,
    override val deviceType: DeviceType,
    val rssi: Int? = null,
    val isBonded: Boolean = false,
    val serviceUuids: List<String> = emptyList()
) : CommunicationDevice() {
    override val protocol: CommunicationProtocol = when (deviceType) {
        DeviceType.BLUETOOTH_CLASSIC -> CommunicationProtocol.BLUETOOTH_CLASSIC
        DeviceType.BLUETOOTH_BLE -> CommunicationProtocol.BLUETOOTH_BLE
        else -> CommunicationProtocol.UNKNOWN
    }
    
    override val isAvailable: Boolean = true
    
    override val properties: Map<String, Any> = mapOf(
        "rssi" to (rssi ?: 0),
        "isBonded" to isBonded,
        "serviceUuids" to serviceUuids
    )
}

/**
 * Represents an MQTT broker
 */
data class MqttBroker(
    override val id: String,
    override val name: String,
    override val address: String,
    val port: Int = 1883,
    val username: String? = null,
    val password: String? = null,
    val useTls: Boolean = false,
    val clientId: String? = null
) : CommunicationDevice() {
    override val protocol: CommunicationProtocol = CommunicationProtocol.MQTT
    override val deviceType: DeviceType = DeviceType.MQTT_BROKER
    override val isAvailable: Boolean = true
    
    override val properties: Map<String, Any> = mapOf(
        "port" to port,
        "username" to (username ?: ""),
        "useTls" to useTls,
        "clientId" to (clientId ?: "")
    )
}

/**
 * Represents a TCP server
 */
data class TcpServer(
    override val id: String,
    override val name: String,
    override val address: String,
    val port: Int
) : CommunicationDevice() {
    override val protocol: CommunicationProtocol = CommunicationProtocol.TCP
    override val deviceType: DeviceType = DeviceType.TCP_SERVER
    override val isAvailable: Boolean = true
    
    override val properties: Map<String, Any> = mapOf(
        "port" to port
    )
}

/**
 * Represents a UDP server
 */
data class UdpServer(
    override val id: String,
    override val name: String,
    override val address: String,
    val port: Int
) : CommunicationDevice() {
    override val protocol: CommunicationProtocol = CommunicationProtocol.UDP
    override val deviceType: DeviceType = DeviceType.UDP_SERVER
    override val isAvailable: Boolean = true
    
    override val properties: Map<String, Any> = mapOf(
        "port" to port
    )
}
