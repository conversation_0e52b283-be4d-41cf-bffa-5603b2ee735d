package com.tfkcolin.cebsscada.communication.models

import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.MessagePriority
import com.tfkcolin.cebsscada.communication.QoSLevel

/**
 * Represents a message in the communication system
 */
data class CommunicationMessage(
    val id: String = generateMessageId(),
    val deviceId: String,
    val protocol: CommunicationProtocol,
    val content: ByteArray,
    val timestamp: Long = System.currentTimeMillis(),
    val direction: MessageDirection,
    val priority: MessagePriority = MessagePriority.NORMAL,
    val qosLevel: QoSLevel = QoSLevel.AT_LEAST_ONCE,
    val topic: String? = null, // For MQTT and similar protocols
    val retained: Boolean = false, // For MQTT
    val metadata: Map<String, Any> = emptyMap()
) {
    
    /**
     * Get the message content as a string
     */
    val contentAsString: String
        get() = String(content, Charsets.UTF_8)
    
    /**
     * Get the message size in bytes
     */
    val size: Int
        get() = content.size
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        
        other as CommunicationMessage
        
        if (id != other.id) return false
        if (deviceId != other.deviceId) return false
        if (protocol != other.protocol) return false
        if (!content.contentEquals(other.content)) return false
        if (timestamp != other.timestamp) return false
        if (direction != other.direction) return false
        if (priority != other.priority) return false
        if (qosLevel != other.qosLevel) return false
        if (topic != other.topic) return false
        if (retained != other.retained) return false
        if (metadata != other.metadata) return false
        
        return true
    }
    
    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + deviceId.hashCode()
        result = 31 * result + protocol.hashCode()
        result = 31 * result + content.contentHashCode()
        result = 31 * result + timestamp.hashCode()
        result = 31 * result + direction.hashCode()
        result = 31 * result + priority.hashCode()
        result = 31 * result + qosLevel.hashCode()
        result = 31 * result + (topic?.hashCode() ?: 0)
        result = 31 * result + retained.hashCode()
        result = 31 * result + metadata.hashCode()
        return result
    }
    
    companion object {
        private var messageCounter = 0L
        
        private fun generateMessageId(): String {
            return "msg_${System.currentTimeMillis()}_${++messageCounter}"
        }
        
        /**
         * Create a simple text message
         */
        fun createTextMessage(
            deviceId: String,
            protocol: CommunicationProtocol,
            text: String,
            direction: MessageDirection,
            topic: String? = null
        ): CommunicationMessage {
            return CommunicationMessage(
                deviceId = deviceId,
                protocol = protocol,
                content = text.toByteArray(Charsets.UTF_8),
                direction = direction,
                topic = topic
            )
        }
        
        /**
         * Create a binary message
         */
        fun createBinaryMessage(
            deviceId: String,
            protocol: CommunicationProtocol,
            data: ByteArray,
            direction: MessageDirection,
            topic: String? = null
        ): CommunicationMessage {
            return CommunicationMessage(
                deviceId = deviceId,
                protocol = protocol,
                content = data,
                direction = direction,
                topic = topic
            )
        }
    }
}

/**
 * Enum representing the direction of a message
 */
enum class MessageDirection {
    INCOMING,
    OUTGOING
}

/**
 * Represents the result of a message send operation
 */
data class MessageResult(
    val success: Boolean,
    val messageId: String,
    val error: String? = null,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * Represents a subscription to a topic (for MQTT and similar protocols)
 */
data class TopicSubscription(
    val topic: String,
    val qosLevel: QoSLevel = QoSLevel.AT_LEAST_ONCE,
    val timestamp: Long = System.currentTimeMillis()
)
