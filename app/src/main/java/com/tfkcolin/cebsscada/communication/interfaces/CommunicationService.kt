package com.tfkcolin.cebsscada.communication.interfaces

import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.communication.models.CommunicationDevice
import com.tfkcolin.cebsscada.communication.models.CommunicationMessage
import com.tfkcolin.cebsscada.communication.models.MessageResult
import com.tfkcolin.cebsscada.communication.models.TopicSubscription
import kotlinx.coroutines.flow.Flow

/**
 * Interface defining the contract for all communication services
 */
interface CommunicationService {
    
    /**
     * Flow of connection state changes
     */
    val connectionState: Flow<ConnectionState>
    
    /**
     * Flow of incoming messages
     */
    val incomingMessages: Flow<CommunicationMessage>
    
    /**
     * Flow of connection errors
     */
    val connectionErrors: Flow<String>
    
    /**
     * Currently connected device, null if not connected
     */
    val connectedDevice: CommunicationDevice?
    
    /**
     * Check if the service is currently connected
     */
    val isConnected: Boolean
    
    /**
     * Connect to a device
     * @param device The device to connect to
     * @param options Additional connection options
     * @return true if connection attempt started successfully
     */
    suspend fun connect(device: CommunicationDevice, options: Map<String, Any> = emptyMap()): Boolean
    
    /**
     * Disconnect from the current device
     */
    suspend fun disconnect()
    
    /**
     * Send a message to the connected device
     * @param message The message to send
     * @return Result of the send operation
     */
    suspend fun sendMessage(message: CommunicationMessage): MessageResult
    
    /**
     * Send raw data to the connected device
     * @param data The raw data to send
     * @return Result of the send operation
     */
    suspend fun sendData(data: ByteArray): MessageResult
    
    /**
     * Send a text message to the connected device
     * @param text The text to send
     * @param topic Optional topic for protocols that support it (like MQTT)
     * @return Result of the send operation
     */
    suspend fun sendText(text: String, topic: String? = null): MessageResult
    
    /**
     * Get the list of available devices for this communication protocol
     * @return Flow of available devices
     */
    fun getAvailableDevices(): Flow<List<CommunicationDevice>>
    
    /**
     * Start scanning/discovering devices
     */
    suspend fun startDiscovery()
    
    /**
     * Stop scanning/discovering devices
     */
    suspend fun stopDiscovery()
    
    /**
     * Check if discovery is currently active
     */
    val isDiscovering: Boolean
    
    /**
     * Subscribe to a topic (for protocols that support it like MQTT)
     * @param subscription The subscription details
     * @return true if subscription was successful
     */
    suspend fun subscribe(subscription: TopicSubscription): Boolean
    
    /**
     * Unsubscribe from a topic (for protocols that support it like MQTT)
     * @param topic The topic to unsubscribe from
     * @return true if unsubscription was successful
     */
    suspend fun unsubscribe(topic: String): Boolean
    
    /**
     * Get current subscriptions (for protocols that support it like MQTT)
     * @return Flow of current subscriptions
     */
    fun getSubscriptions(): Flow<List<TopicSubscription>>
    
    /**
     * Get connection statistics
     * @return Map of statistics
     */
    fun getConnectionStats(): Map<String, Any>
    
    /**
     * Clean up resources
     */
    suspend fun cleanup()
}
