package com.tfkcolin.cebsscada.communication.bluetooth

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice as AndroidBluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCallback
import android.bluetooth.BluetoothProfile
import android.bluetooth.BluetoothSocket
import android.content.Context
import android.util.Log
import androidx.annotation.RequiresPermission
import com.tfkcolin.cebsscada.bluetooth.BluetoothConstants
import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.communication.DeviceType
import com.tfkcolin.cebsscada.communication.base.BaseCommunicationService
import com.tfkcolin.cebsscada.communication.models.BluetoothDevice
import com.tfkcolin.cebsscada.communication.models.CommunicationDevice
import com.tfkcolin.cebsscada.communication.models.CommunicationMessage
import com.tfkcolin.cebsscada.communication.models.MessageDirection
import com.tfkcolin.cebsscada.communication.models.MessageResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.io.IOException
import java.util.UUID
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import javax.inject.Inject

/**
 * Bluetooth communication service implementing the new architecture
 */
class BluetoothCommunicationService @Inject constructor(
    private val context: Context,
    private val bluetoothAdapter: BluetoothAdapter?
) : BaseCommunicationService() {
    
    companion object {
        private const val TAG = "BluetoothCommService"
        private val UUID_SPP = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB")
    }
    
    private var socket: BluetoothSocket? = null
    private var bleGatt: BluetoothGatt? = null
    private var connectedThread: ConnectedThread? = null
    private val isConnecting = AtomicBoolean(false)

    // Connection reliability features
    private var reconnectionAttempts = AtomicInteger(0)
    private var maxReconnectionAttempts = 3
    private var reconnectionDelayMs = 2000L
    private var connectionTimeoutMs = 15000L
    private var lastConnectionTime = 0L
    private var connectionQualityRssi = -100
    private var isAutoReconnectEnabled = true
    
    override suspend fun connect(device: CommunicationDevice, options: Map<String, Any>): Boolean {
        if (device !is BluetoothDevice) {
            emitConnectionError("Invalid device type for Bluetooth service")
            return false
        }
        
        if (bluetoothAdapter == null) {
            emitConnectionError("Bluetooth adapter not available")
            return false
        }
        
        if (isConnected || isConnecting.get()) {
            disconnect()
        }
        
        isConnecting.set(true)
        updateConnectionState(ConnectionState.CONNECTING)
        updateConnectedDevice(device)
        
        return try {
            when (device.deviceType) {
                DeviceType.BLUETOOTH_CLASSIC -> {
                    connectClassic(device)
                }
                DeviceType.BLUETOOTH_BLE -> {
                    connectBLE(device)
                }
                else -> {
                    emitConnectionError("Unsupported Bluetooth device type")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Connection failed", e)
            updateConnectionState(ConnectionState.ERROR)
            emitConnectionError("Connection failed: ${e.message}")
            false
        } finally {
            isConnecting.set(false)
        }
    }
    
    
    private suspend fun connectClassic(device: BluetoothDevice): Boolean = withContext(Dispatchers.IO) {
        try {
            val androidDevice = bluetoothAdapter?.getRemoteDevice(device.address)
            val tmpSocket = androidDevice?.createRfcommSocketToServiceRecord(UUID_SPP)
            
            bluetoothAdapter?.cancelDiscovery()
            tmpSocket?.connect()
            
            socket = tmpSocket
            connectedThread = ConnectedThread(socket)
            connectedThread?.start()
            
            updateConnectionState(ConnectionState.CONNECTED)
            true
        } catch (e: IOException) {
            Log.e(TAG, "Classic Bluetooth connection failed", e)
            updateConnectionState(ConnectionState.ERROR)
            emitConnectionError("Classic Bluetooth connection failed: ${e.message}")
            closeSocket()
            false
        }
    }
    
    
    private suspend fun connectBLE(device: BluetoothDevice): Boolean = withContext(Dispatchers.Main) {
        try {
            val androidDevice = bluetoothAdapter?.getRemoteDevice(device.address)
            val callback = object : BluetoothGattCallback() {
                
                override fun onConnectionStateChange(gatt: BluetoothGatt?, status: Int, newState: Int) {
                    when (newState) {
                        BluetoothProfile.STATE_CONNECTED -> {
                            Log.d(TAG, "BLE connected successfully")
                            reconnectionAttempts.set(0) // Reset reconnection attempts on successful connection
                            lastConnectionTime = System.currentTimeMillis()
                            updateConnectionState(ConnectionState.CONNECTED)
                            gatt?.discoverServices()
                        }
                        BluetoothProfile.STATE_DISCONNECTED -> {
                            Log.d(TAG, "BLE disconnected, status: $status")
                            updateConnectionState(ConnectionState.DISCONNECTED)

                            // Handle unexpected disconnections with auto-reconnect
                            if (status != BluetoothGatt.GATT_SUCCESS && isAutoReconnectEnabled) {
                                val attempts = reconnectionAttempts.get()
                                if (attempts < maxReconnectionAttempts) {
                                    Log.d(TAG, "Attempting reconnection ${attempts + 1}/$maxReconnectionAttempts")
                                    reconnectionAttempts.incrementAndGet()

                                    // Schedule reconnection with exponential backoff
                                    val delay = reconnectionDelayMs * (attempts + 1)
                                    runBlocking {
                                        delay(delay)
                                        if (connectedDevice != null) {
                                            Log.d(TAG, "Executing reconnection attempt")
                                            updateConnectionState(ConnectionState.RECONNECTING)
                                            connectBLE(connectedDevice as BluetoothDevice)
                                        }
                                    }
                                } else {
                                    Log.e(TAG, "Max reconnection attempts reached")
                                    runBlocking {
                                        emitConnectionError("Connection lost after $maxReconnectionAttempts reconnection attempts")
                                    }
                                }
                            }

                            closeBLEConnection()
                        }
                    }
                }

                override fun onServicesDiscovered(gatt: BluetoothGatt?, status: Int) {
                    if (status == BluetoothGatt.GATT_SUCCESS) {
                        Log.d(TAG, "BLE services discovered: ${gatt?.services?.size} services")
                        gatt?.services?.forEach { service ->
                            Log.d(TAG, "Service: ${service.uuid}")
                            service.characteristics.forEach { characteristic ->
                                Log.d(TAG, "  Characteristic: ${characteristic.uuid}, Properties: ${characteristic.properties}")
                            }
                        }
                    } else {
                        Log.e(TAG, "Service discovery failed with status: $status")
                        kotlinx.coroutines.runBlocking {
                            emitConnectionError("Service discovery failed")
                        }
                    }
                }

                override fun onCharacteristicRead(gatt: BluetoothGatt?, characteristic: android.bluetooth.BluetoothGattCharacteristic?, status: Int) {
                    if (status == BluetoothGatt.GATT_SUCCESS) {
                        characteristic?.value?.let { data ->
                            val message = CommunicationMessage.createBinaryMessage(
                                deviceId = device.id,
                                protocol = device.protocol,
                                data = data,
                                direction = MessageDirection.INCOMING
                            )
                            kotlinx.coroutines.runBlocking {
                                emitIncomingMessage(message)
                            }
                            Log.d(TAG, "Characteristic read: ${characteristic.uuid}, Data: ${data.contentToString()}")
                        }
                    } else {
                        Log.e(TAG, "Characteristic read failed: ${characteristic?.uuid}, Status: $status")
                        kotlinx.coroutines.runBlocking {
                            emitConnectionError("Characteristic read failed")
                        }
                    }
                }

                override fun onCharacteristicWrite(gatt: BluetoothGatt?, characteristic: android.bluetooth.BluetoothGattCharacteristic?, status: Int) {
                    if (status == BluetoothGatt.GATT_SUCCESS) {
                        Log.d(TAG, "Characteristic write successful: ${characteristic?.uuid}")
                    } else {
                        Log.e(TAG, "Characteristic write failed: ${characteristic?.uuid}, Status: $status")
                        kotlinx.coroutines.runBlocking {
                            emitConnectionError("Characteristic write failed")
                        }
                    }
                }

                override fun onCharacteristicChanged(gatt: BluetoothGatt?, characteristic: android.bluetooth.BluetoothGattCharacteristic?) {
                    characteristic?.value?.let { data ->
                        val message = CommunicationMessage.createBinaryMessage(
                            deviceId = device.id,
                            protocol = device.protocol,
                            data = data,
                            direction = MessageDirection.INCOMING
                        )
                        kotlinx.coroutines.runBlocking {
                            emitIncomingMessage(message)
                        }
                        Log.d(TAG, "Characteristic notification: ${characteristic.uuid}, Data: ${data.contentToString()}")
                    }
                }

                override fun onDescriptorWrite(gatt: BluetoothGatt?, descriptor: android.bluetooth.BluetoothGattDescriptor?, status: Int) {
                    if (status == BluetoothGatt.GATT_SUCCESS) {
                        Log.d(TAG, "Descriptor write successful: ${descriptor?.uuid}")
                    } else {
                        Log.e(TAG, "Descriptor write failed: ${descriptor?.uuid}, Status: $status")
                        kotlinx.coroutines.runBlocking {
                            emitConnectionError("Descriptor write failed")
                        }
                    }
                }
            }

            bleGatt = androidDevice?.connectGatt(context, false, callback)
            true
        } catch (e: Exception) {
            Log.e(TAG, "BLE connection failed", e)
            updateConnectionState(ConnectionState.ERROR)
            emitConnectionError("BLE connection failed: ${e.message}")
            false
        }
    }
    
    override suspend fun disconnect() {
        updateConnectionState(ConnectionState.DISCONNECTING)
        
        connectedThread?.cancel()
        connectedThread = null
        
        closeSocket()
        closeBLEConnection()
        
        updateConnectionState(ConnectionState.DISCONNECTED)
        updateConnectedDevice(null)
    }
    
    
    override suspend fun sendMessage(message: CommunicationMessage): MessageResult {
        if (!isConnected) {
            return MessageResult(
                success = false,
                messageId = message.id,
                error = "Not connected to device"
            )
        }
        
        return try {
            when (connectedDevice?.deviceType) {
                DeviceType.BLUETOOTH_CLASSIC -> {
                    sendClassicMessage(message)
                }
                DeviceType.BLUETOOTH_BLE -> {
                    sendBLEMessage(message)
                }
                else -> {
                    MessageResult(
                        success = false,
                        messageId = message.id,
                        error = "Unsupported device type"
                    )
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send message", e)
            MessageResult(
                success = false,
                messageId = message.id,
                error = "Send failed: ${e.message}"
            )
        }
    }
    
    private suspend fun sendClassicMessage(message: CommunicationMessage): MessageResult {
        return try {
            socket?.outputStream?.write(message.content)
            trackSentMessage(message)
            MessageResult(success = true, messageId = message.id)
        } catch (e: IOException) {
            MessageResult(
                success = false,
                messageId = message.id,
                error = "Write failed: ${e.message}"
            )
        }
    }
    
    
    private suspend fun sendBLEMessage(message: CommunicationMessage): MessageResult {
        val gatt = bleGatt ?: return MessageResult(
            success = false,
            messageId = message.id,
            error = "BLE GATT not connected"
        )

        // Extract service and characteristic UUIDs from message metadata or use defaults
        val serviceUuidString = message.metadata["serviceUuid"] as? String
        val characteristicUuidString = message.metadata["characteristicUuid"] as? String

        if (serviceUuidString == null || characteristicUuidString == null) {
            return MessageResult(
                success = false,
                messageId = message.id,
                error = "Service UUID and Characteristic UUID are required for BLE messages"
            )
        }

        return try {
            val serviceUuid = java.util.UUID.fromString(serviceUuidString)
            val characteristicUuid = java.util.UUID.fromString(characteristicUuidString)

            val service = gatt.getService(serviceUuid)
            if (service == null) {
                return MessageResult(
                    success = false,
                    messageId = message.id,
                    error = "Service not found: $serviceUuidString"
                )
            }

            val characteristic = service.getCharacteristic(characteristicUuid)
            if (characteristic == null) {
                return MessageResult(
                    success = false,
                    messageId = message.id,
                    error = "Characteristic not found: $characteristicUuidString"
                )
            }

            // Check if characteristic supports writing
            val canWrite = (characteristic.properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_WRITE) != 0 ||
                          (characteristic.properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0

            if (!canWrite) {
                return MessageResult(
                    success = false,
                    messageId = message.id,
                    error = "Characteristic does not support writing"
                )
            }

            // Set the value and write type
            characteristic.value = message.content
            characteristic.writeType = if ((characteristic.properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0) {
                android.bluetooth.BluetoothGattCharacteristic.WRITE_TYPE_NO_RESPONSE
            } else {
                android.bluetooth.BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT
            }

            // Perform the write operation
            val writeResult = gatt.writeCharacteristic(characteristic)

            if (writeResult) {
                trackSentMessage(message)
                MessageResult(success = true, messageId = message.id)
            } else {
                MessageResult(
                    success = false,
                    messageId = message.id,
                    error = "Failed to initiate characteristic write"
                )
            }

        } catch (e: IllegalArgumentException) {
            MessageResult(
                success = false,
                messageId = message.id,
                error = "Invalid UUID format: ${e.message}"
            )
        } catch (e: Exception) {
            MessageResult(
                success = false,
                messageId = message.id,
                error = "BLE write failed: ${e.message}"
            )
        }
    }
    
    
    override suspend fun startDiscovery() {
        updateDiscoveryState(true)
        bluetoothAdapter?.startDiscovery()
    }
    
    
    override suspend fun stopDiscovery() {
        bluetoothAdapter?.cancelDiscovery()
        updateDiscoveryState(false)
    }
    
    override suspend fun cleanup() {
        disconnect()
        connectedThread?.cancel()
    }

    /**
     * Read a BLE characteristic
     */
    
    suspend fun readCharacteristic(serviceUuid: String, characteristicUuid: String): Boolean {
        val gatt = bleGatt ?: return false

        return try {
            val service = gatt.getService(java.util.UUID.fromString(serviceUuid))
            val characteristic = service?.getCharacteristic(java.util.UUID.fromString(characteristicUuid))

            if (characteristic == null) {
                emitConnectionError("Characteristic not found: $characteristicUuid")
                return false
            }

            // Check if characteristic supports reading
            val canRead = (characteristic.properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_READ) != 0
            if (!canRead) {
                emitConnectionError("Characteristic does not support reading")
                return false
            }

            gatt.readCharacteristic(characteristic)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to read characteristic", e)
            emitConnectionError("Read characteristic failed: ${e.message}")
            false
        }
    }

    /**
     * Enable notifications for a BLE characteristic
     */
    
    suspend fun enableNotifications(serviceUuid: String, characteristicUuid: String): Boolean {
        val gatt = bleGatt ?: return false

        return try {
            val service = gatt.getService(java.util.UUID.fromString(serviceUuid))
            val characteristic = service?.getCharacteristic(java.util.UUID.fromString(characteristicUuid))

            if (characteristic == null) {
                emitConnectionError("Characteristic not found: $characteristicUuid")
                return false
            }

            // Check if characteristic supports notifications
            val canNotify = (characteristic.properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0 ||
                           (characteristic.properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0

            if (!canNotify) {
                emitConnectionError("Characteristic does not support notifications")
                return false
            }

            // Enable local notifications
            val notificationEnabled = gatt.setCharacteristicNotification(characteristic, true)
            if (!notificationEnabled) {
                emitConnectionError("Failed to enable local notifications")
                return false
            }

            // Write to the Client Characteristic Configuration Descriptor
            val cccdUuid = java.util.UUID.fromString("00002902-0000-1000-8000-00805f9b34fb")
            val descriptor = characteristic.getDescriptor(cccdUuid)

            if (descriptor != null) {
                val value = if ((characteristic.properties and android.bluetooth.BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0) {
                    android.bluetooth.BluetoothGattDescriptor.ENABLE_INDICATION_VALUE
                } else {
                    android.bluetooth.BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
                }

                descriptor.value = value
                gatt.writeDescriptor(descriptor)
            } else {
                Log.w(TAG, "CCCD not found for characteristic: $characteristicUuid")
                true // Some characteristics might not have CCCD
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to enable notifications", e)
            emitConnectionError("Enable notifications failed: ${e.message}")
            false
        }
    }

    /**
     * Disable notifications for a BLE characteristic
     */
    
    suspend fun disableNotifications(serviceUuid: String, characteristicUuid: String): Boolean {
        val gatt = bleGatt ?: return false

        return try {
            val service = gatt.getService(java.util.UUID.fromString(serviceUuid))
            val characteristic = service?.getCharacteristic(java.util.UUID.fromString(characteristicUuid))

            if (characteristic == null) {
                emitConnectionError("Characteristic not found: $characteristicUuid")
                return false
            }

            // Disable local notifications
            val notificationDisabled = gatt.setCharacteristicNotification(characteristic, false)
            if (!notificationDisabled) {
                emitConnectionError("Failed to disable local notifications")
                return false
            }

            // Write to the Client Characteristic Configuration Descriptor
            val cccdUuid = java.util.UUID.fromString("00002902-0000-1000-8000-00805f9b34fb")
            val descriptor = characteristic.getDescriptor(cccdUuid)

            if (descriptor != null) {
                descriptor.value = android.bluetooth.BluetoothGattDescriptor.DISABLE_NOTIFICATION_VALUE
                gatt.writeDescriptor(descriptor)
            } else {
                Log.w(TAG, "CCCD not found for characteristic: $characteristicUuid")
                true // Some characteristics might not have CCCD
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to disable notifications", e)
            emitConnectionError("Disable notifications failed: ${e.message}")
            false
        }
    }

    /**
     * Get available BLE services
     */
    fun getAvailableServices(): List<android.bluetooth.BluetoothGattService>? {
        return bleGatt?.services
    }

    /**
     * Send data to a specific BLE characteristic
     */
    
    suspend fun sendToBLECharacteristic(data: ByteArray, serviceUuid: String, characteristicUuid: String): MessageResult {
        val device = connectedDevice ?: return MessageResult(
            success = false,
            messageId = "",
            error = "No device connected"
        )

        val message = CommunicationMessage(
            deviceId = device.id,
            protocol = device.protocol,
            content = data,
            direction = MessageDirection.OUTGOING,
            metadata = mapOf(
                "serviceUuid" to serviceUuid,
                "characteristicUuid" to characteristicUuid
            )
        )

        return sendBLEMessage(message)
    }

    /**
     * Enable or disable automatic reconnection
     */
    fun setAutoReconnectEnabled(enabled: Boolean) {
        isAutoReconnectEnabled = enabled
        Log.d(TAG, "Auto-reconnect ${if (enabled) "enabled" else "disabled"}")
    }

    /**
     * Set maximum reconnection attempts
     */
    fun setMaxReconnectionAttempts(maxAttempts: Int) {
        maxReconnectionAttempts = maxAttempts
        Log.d(TAG, "Max reconnection attempts set to $maxAttempts")
    }

    /**
     * Set reconnection delay in milliseconds
     */
    fun setReconnectionDelay(delayMs: Long) {
        reconnectionDelayMs = delayMs
        Log.d(TAG, "Reconnection delay set to ${delayMs}ms")
    }

    /**
     * Get connection quality information
     */
    fun getConnectionQuality(): Map<String, Any> {
        val uptime = if (lastConnectionTime > 0) {
            System.currentTimeMillis() - lastConnectionTime
        } else 0L

        return mapOf(
            "isConnected" to isConnected,
            "connectionUptime" to uptime,
            "reconnectionAttempts" to reconnectionAttempts.get(),
            "maxReconnectionAttempts" to maxReconnectionAttempts,
            "rssi" to connectionQualityRssi,
            "autoReconnectEnabled" to isAutoReconnectEnabled,
            "connectionType" to when (connectedDevice?.deviceType) {
                com.tfkcolin.cebsscada.communication.DeviceType.BLUETOOTH_CLASSIC -> "Classic"
                com.tfkcolin.cebsscada.communication.DeviceType.BLUETOOTH_BLE -> "BLE"
                else -> "Unknown"
            }
        )
    }

    /**
     * Force reconnection attempt
     */
    
    suspend fun forceReconnect(): Boolean {
        val device = connectedDevice ?: return false

        Log.d(TAG, "Force reconnecting to ${device.name}")
        disconnect()
        delay(1000) // Brief delay before reconnection

        return connect(device, emptyMap())
    }

    /**
     * Test connection by sending a ping-like operation
     */
    suspend fun testConnection(): Boolean {
        if (!isConnected) return false

        return when (connectedDevice?.deviceType) {
            com.tfkcolin.cebsscada.communication.DeviceType.BLUETOOTH_BLE -> {
                // For BLE, try to read a characteristic or check GATT services
                bleGatt?.services?.isNotEmpty() == true
            }
            com.tfkcolin.cebsscada.communication.DeviceType.BLUETOOTH_CLASSIC -> {
                // For Classic, check if socket is still connected
                socket?.isConnected == true
            }
            else -> false
        }
    }

    /**
     * Monitor connection health periodically
     */
    
    suspend fun startConnectionMonitoring(intervalMs: Long = 30000L) {
        while (isConnected) {
            delay(intervalMs)

            if (isConnected && !testConnection()) {
                Log.w(TAG, "Connection health check failed")
                emitConnectionError("Connection health check failed")

                if (isAutoReconnectEnabled) {
                    forceReconnect()
                }
            }
        }
    }
    
    private fun closeSocket() {
        try {
            socket?.close()
        } catch (e: IOException) {
            Log.e(TAG, "Error closing socket", e)
        } finally {
            socket = null
        }
    }
    
    
    private fun closeBLEConnection() {
        bleGatt?.close()
        bleGatt = null
    }
    
    /**
     * Thread for handling Classic Bluetooth communication
     */
    private inner class ConnectedThread(private val socket: BluetoothSocket?) {
        private val inputStream = socket?.inputStream
        private val outputStream = socket?.outputStream
        private val buffer = ByteArray(1024)
        private val shouldContinue = AtomicBoolean(true)
        
        private val readThread = Thread {
            while (shouldContinue.get()) {
                try {
                    val bytes = inputStream?.read(buffer) ?: break
                    val data = buffer.copyOf(bytes)
                    
                    val device = connectedDevice as? BluetoothDevice ?: return@Thread
                    val message = CommunicationMessage.createBinaryMessage(
                        deviceId = device.id,
                        protocol = device.protocol,
                        data = data,
                        direction = MessageDirection.INCOMING
                    )
                    
                    kotlinx.coroutines.runBlocking {
                        emitIncomingMessage(message)
                    }
                } catch (e: IOException) {
                    if (shouldContinue.get()) {
                        updateConnectionState(ConnectionState.ERROR)
                        kotlinx.coroutines.runBlocking {
                            emitConnectionError("Connection lost: ${e.message}")
                        }
                    }
                    break
                }
            }
        }
        
        fun start() {
            readThread.start()
        }
        
        fun cancel() {
            shouldContinue.set(false)
            readThread.interrupt()
        }
    }
}
