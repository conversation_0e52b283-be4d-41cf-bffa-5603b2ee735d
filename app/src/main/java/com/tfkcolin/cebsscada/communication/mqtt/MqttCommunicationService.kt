package com.tfkcolin.cebsscada.communication.mqtt

import android.content.Context
import android.util.Log
import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.communication.QoSLevel
import com.tfkcolin.cebsscada.communication.base.BaseCommunicationService
import com.tfkcolin.cebsscada.communication.models.CommunicationDevice
import com.tfkcolin.cebsscada.communication.models.CommunicationMessage
import com.tfkcolin.cebsscada.communication.models.MessageDirection
import com.tfkcolin.cebsscada.communication.models.MessageResult
import com.tfkcolin.cebsscada.communication.models.MqttBroker
import com.tfkcolin.cebsscada.communication.models.TopicSubscription
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.eclipse.paho.client.mqttv3.IMqttActionListener
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken
import org.eclipse.paho.client.mqttv3.IMqttToken
import org.eclipse.paho.client.mqttv3.MqttAsyncClient
import org.eclipse.paho.client.mqttv3.MqttCallback
import org.eclipse.paho.client.mqttv3.MqttConnectOptions
import org.eclipse.paho.client.mqttv3.MqttException
import org.eclipse.paho.client.mqttv3.MqttMessage
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject

/**
 * MQTT communication service implementing the new architecture
 */
class MqttCommunicationService @Inject constructor(
    private val context: Context
) : BaseCommunicationService(), MqttCallback {
    
    companion object {
        private const val TAG = "MqttCommService"
        private const val CONNECTION_TIMEOUT = 10000 // 10 seconds
        private const val KEEP_ALIVE_INTERVAL = 60 // seconds
        private const val DEFAULT_QOS = 1
    }
    
    private var mqttClient: MqttAsyncClient? = null
    private val isConnecting = AtomicBoolean(false)
    private val subscriptionMap = mutableMapOf<String, TopicSubscription>()
    
    override suspend fun connect(device: CommunicationDevice, options: Map<String, Any>): Boolean {
        if (device !is MqttBroker) {
            emitConnectionError("Invalid device type for MQTT service")
            return false
        }
        
        if (isConnected || isConnecting.get()) {
            disconnect()
        }
        
        isConnecting.set(true)
        updateConnectionState(ConnectionState.CONNECTING)
        updateConnectedDevice(device)
        
        return try {
            connectToMqttBroker(device, options)
        } catch (e: Exception) {
            Log.e(TAG, "MQTT connection failed", e)
            updateConnectionState(ConnectionState.ERROR)
            emitConnectionError("MQTT connection failed: ${e.message}")
            false
        } finally {
            isConnecting.set(false)
        }
    }
    
    private suspend fun connectToMqttBroker(broker: MqttBroker, options: Map<String, Any>): Boolean = withContext(Dispatchers.IO) {
        try {
            val brokerUrl = if (broker.useTls) {
                "ssl://${broker.address}:${broker.port}"
            } else {
                "tcp://${broker.address}:${broker.port}"
            }
            
            val clientId = broker.clientId ?: "android_mqtt_client_${System.currentTimeMillis()}"
            
            mqttClient = MqttAsyncClient(brokerUrl, clientId, MemoryPersistence())
            mqttClient?.setCallback(this@MqttCommunicationService)
            
            val connectOptions = MqttConnectOptions().apply {
                isCleanSession = options["cleanSession"] as? Boolean ?: true
                connectionTimeout = CONNECTION_TIMEOUT
                keepAliveInterval = KEEP_ALIVE_INTERVAL
                isAutomaticReconnect = options["autoReconnect"] as? Boolean ?: true
                
                broker.username?.let { userName = it }
                broker.password?.let { password = it.toCharArray() }
            }
            
            val connectResult = kotlinx.coroutines.suspendCancellableCoroutine<Boolean> { continuation ->
                mqttClient?.connect(connectOptions, null, object : IMqttActionListener {
                    override fun onSuccess(asyncActionToken: IMqttToken?) {
                        updateConnectionState(ConnectionState.CONNECTED)
                        Log.d(TAG, "Connected to MQTT broker: ${broker.address}")
                        continuation.resumeWith(Result.success(true))
                    }
                    
                    override fun onFailure(asyncActionToken: IMqttToken?, exception: Throwable?) {
                        updateConnectionState(ConnectionState.ERROR)
                        val errorMsg = "MQTT connection failed: ${exception?.message}"
                        Log.e(TAG, errorMsg, exception)
                        kotlinx.coroutines.runBlocking {
                            emitConnectionError(errorMsg)
                        }
                        continuation.resumeWith(Result.success(false))
                    }
                })
            }
            
            connectResult
        } catch (e: MqttException) {
            Log.e(TAG, "MQTT connection exception", e)
            updateConnectionState(ConnectionState.ERROR)
            emitConnectionError("MQTT connection exception: ${e.message}")
            false
        }
    }
    
    override suspend fun disconnect() {
        updateConnectionState(ConnectionState.DISCONNECTING)
        
        try {
            mqttClient?.let { client ->
                if (client.isConnected) {
                    val disconnectResult = kotlinx.coroutines.suspendCancellableCoroutine<Boolean> { continuation ->
                        client.disconnect(null, object : IMqttActionListener {
                            override fun onSuccess(asyncActionToken: IMqttToken?) {
                                Log.d(TAG, "Disconnected from MQTT broker")
                                continuation.resumeWith(Result.success(true))
                            }
                            
                            override fun onFailure(asyncActionToken: IMqttToken?, exception: Throwable?) {
                                Log.e(TAG, "MQTT disconnection failed", exception)
                                continuation.resumeWith(Result.success(false))
                            }
                        })
                    }
                }
                client.close()
            }
        } catch (e: MqttException) {
            Log.e(TAG, "Error during MQTT disconnect", e)
        } finally {
            mqttClient = null
            subscriptionMap.clear()
            updateSubscriptions(emptyList())
            updateConnectionState(ConnectionState.DISCONNECTED)
            updateConnectedDevice(null)
        }
    }
    
    override suspend fun sendMessage(message: CommunicationMessage): MessageResult {
        if (!isConnected) {
            return MessageResult(
                success = false,
                messageId = message.id,
                error = "Not connected to MQTT broker"
            )
        }
        
        val topic = message.topic ?: return MessageResult(
            success = false,
            messageId = message.id,
            error = "Topic is required for MQTT messages"
        )
        
        return try {
            publishMessage(topic, message)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send MQTT message", e)
            MessageResult(
                success = false,
                messageId = message.id,
                error = "Send failed: ${e.message}"
            )
        }
    }
    
    private suspend fun publishMessage(topic: String, message: CommunicationMessage): MessageResult {
        return try {
            val qos = when (message.qosLevel) {
                QoSLevel.AT_MOST_ONCE -> 0
                QoSLevel.AT_LEAST_ONCE -> 1
                QoSLevel.EXACTLY_ONCE -> 2
            }
            
            val mqttMessage = MqttMessage(message.content).apply {
                this.qos = qos
                isRetained = message.retained
            }
            
            val publishResult = kotlinx.coroutines.suspendCancellableCoroutine<Boolean> { continuation ->
                mqttClient?.publish(topic, mqttMessage, null, object : IMqttActionListener {
                    override fun onSuccess(asyncActionToken: IMqttToken?) {
                        trackSentMessage(message)
                        Log.d(TAG, "Message published to topic: $topic")
                        continuation.resumeWith(Result.success(true))
                    }
                    
                    override fun onFailure(asyncActionToken: IMqttToken?, exception: Throwable?) {
                        Log.e(TAG, "Publish failed for topic: $topic", exception)
                        continuation.resumeWith(Result.success(false))
                    }
                })
            }
            
            if (publishResult) {
                MessageResult(success = true, messageId = message.id)
            } else {
                MessageResult(success = false, messageId = message.id, error = "Publish failed")
            }
        } catch (e: MqttException) {
            MessageResult(
                success = false,
                messageId = message.id,
                error = "MQTT publish exception: ${e.message}"
            )
        }
    }
    
    override suspend fun subscribe(subscription: TopicSubscription): Boolean {
        if (!isConnected) {
            emitConnectionError("Not connected to MQTT broker")
            return false
        }
        
        return try {
            val qos = when (subscription.qosLevel) {
                QoSLevel.AT_MOST_ONCE -> 0
                QoSLevel.AT_LEAST_ONCE -> 1
                QoSLevel.EXACTLY_ONCE -> 2
            }
            
            val subscribeResult = kotlinx.coroutines.suspendCancellableCoroutine<Boolean> { continuation ->
                mqttClient?.subscribe(subscription.topic, qos, null, object : IMqttActionListener {
                    override fun onSuccess(asyncActionToken: IMqttToken?) {
                        subscriptionMap[subscription.topic] = subscription
                        updateSubscriptions(subscriptionMap.values.toList())
                        Log.d(TAG, "Subscribed to topic: ${subscription.topic}")
                        continuation.resumeWith(Result.success(true))
                    }
                    
                    override fun onFailure(asyncActionToken: IMqttToken?, exception: Throwable?) {
                        Log.e(TAG, "Subscription failed for topic: ${subscription.topic}", exception)
                        kotlinx.coroutines.runBlocking {
                            emitConnectionError("Subscription failed: ${exception?.message}")
                        }
                        continuation.resumeWith(Result.success(false))
                    }
                })
            }
            
            subscribeResult
        } catch (e: MqttException) {
            Log.e(TAG, "Error subscribing to topic: ${subscription.topic}", e)
            emitConnectionError("Subscribe error: ${e.message}")
            false
        }
    }
    
    override suspend fun unsubscribe(topic: String): Boolean {
        if (!isConnected) {
            emitConnectionError("Not connected to MQTT broker")
            return false
        }
        
        return try {
            val unsubscribeResult = kotlinx.coroutines.suspendCancellableCoroutine<Boolean> { continuation ->
                mqttClient?.unsubscribe(topic, null, object : IMqttActionListener {
                    override fun onSuccess(asyncActionToken: IMqttToken?) {
                        subscriptionMap.remove(topic)
                        updateSubscriptions(subscriptionMap.values.toList())
                        Log.d(TAG, "Unsubscribed from topic: $topic")
                        continuation.resumeWith(Result.success(true))
                    }
                    
                    override fun onFailure(asyncActionToken: IMqttToken?, exception: Throwable?) {
                        Log.e(TAG, "Unsubscription failed for topic: $topic", exception)
                        kotlinx.coroutines.runBlocking {
                            emitConnectionError("Unsubscription failed: ${exception?.message}")
                        }
                        continuation.resumeWith(Result.success(false))
                    }
                })
            }
            
            unsubscribeResult
        } catch (e: MqttException) {
            Log.e(TAG, "Error unsubscribing from topic: $topic", e)
            emitConnectionError("Unsubscribe error: ${e.message}")
            false
        }
    }
    
    override suspend fun cleanup() {
        disconnect()
    }
    
    // MqttCallback implementation
    override fun connectionLost(cause: Throwable?) {
        updateConnectionState(ConnectionState.ERROR)
        Log.e(TAG, "MQTT connection lost", cause)
        kotlinx.coroutines.runBlocking {
            emitConnectionError("Connection lost: ${cause?.message}")
        }
    }
    
    override fun messageArrived(topic: String, message: MqttMessage) {
        try {
            val device = connectedDevice as? MqttBroker ?: return
            val commMessage = CommunicationMessage.createBinaryMessage(
                deviceId = device.id,
                protocol = device.protocol,
                data = message.payload,
                direction = MessageDirection.INCOMING,
                topic = topic
            )
            
            kotlinx.coroutines.runBlocking {
                emitIncomingMessage(commMessage)
            }
            
            Log.d(TAG, "Message arrived on topic: $topic")
        } catch (e: Exception) {
            Log.e(TAG, "Error processing incoming message", e)
        }
    }
    
    override fun deliveryComplete(token: IMqttDeliveryToken?) {
        Log.d(TAG, "Message delivery complete")
    }
}
