package com.tfkcolin.cebsscada.communication

/**
 * Enum representing different communication protocols supported by the SCADA system
 */
enum class CommunicationProtocol {
    BLUETOOTH_CLASSIC,
    BLUETOOTH_BLE,
    MQTT,
    TCP,
    UDP,
    SERIAL,
    MODBUS,
    UNKNOWN
}

/**
 * Enum representing the current state of a communication connection
 */
enum class ConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    DISCONNECTING,
    ERROR,
    RECONNECTING
}

/**
 * Enum representing different types of communication devices
 */
enum class DeviceType {
    BLUETOOTH_CLASSIC,
    BLUETOOTH_BLE,
    MQTT_BROKER,
    TCP_SERVER,
    UDP_SERVER,
    SERIAL_DEVICE,
    MODBUS_DEVICE,
    UNKNOWN
}

/**
 * Enum representing message priority levels
 */
enum class MessagePriority {
    LOW,
    NORMAL,
    HIGH,
    CRITICAL
}

/**
 * Enum representing Quality of Service levels for protocols that support it
 */
enum class QoSLevel {
    AT_MOST_ONCE,    // QoS 0
    AT_LEAST_ONCE,   // QoS 1
    EXACTLY_ONCE     // QoS 2
}
