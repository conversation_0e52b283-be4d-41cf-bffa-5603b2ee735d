package com.tfkcolin.cebsscada.communication.factory

import android.bluetooth.BluetoothAdapter
import android.content.Context
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.bluetooth.BluetoothDeviceScanner
import com.tfkcolin.cebsscada.communication.interfaces.DeviceScanner
import com.tfkcolin.cebsscada.communication.interfaces.DeviceScannerFactory
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Factory implementation for creating device scanners
 */
@Singleton
class DeviceScannerFactoryImpl @Inject constructor(
    private val context: Context,
    private val bluetoothAdapter: BluetoothAdapter?
) : DeviceScannerFactory {
    
    override fun createScanner(protocol: CommunicationProtocol): DeviceScanner? {
        return when (protocol) {
            CommunicationProtocol.BLUETOOTH_CLASSIC,
            CommunicationProtocol.BLUETOOTH_BLE -> {
                if (bluetoothAdapter != null) {
                    BluetoothDeviceScanner(context, bluetoothAdapter)
                } else {
                    null
                }
            }
            CommunicationProtocol.MQTT -> {
                // MQTT doesn't support device scanning in the traditional sense
                // Brokers are typically configured manually
                null
            }
            CommunicationProtocol.TCP -> {
                // TODO: Implement TCP scanner (network discovery)
                null
            }
            CommunicationProtocol.UDP -> {
                // TODO: Implement UDP scanner (network discovery)
                null
            }
            CommunicationProtocol.SERIAL -> {
                // TODO: Implement Serial scanner (USB/OTG device discovery)
                null
            }
            CommunicationProtocol.MODBUS -> {
                // TODO: Implement Modbus scanner
                null
            }
            CommunicationProtocol.UNKNOWN -> null
        }
    }
    
    override fun getSupportedProtocols(): List<CommunicationProtocol> {
        val protocols = mutableListOf<CommunicationProtocol>()
        
        // Add Bluetooth protocols if adapter is available
        if (bluetoothAdapter != null) {
            protocols.add(CommunicationProtocol.BLUETOOTH_CLASSIC)
            protocols.add(CommunicationProtocol.BLUETOOTH_BLE)
        }
        
        // TODO: Add other protocols as scanners are implemented
        // protocols.add(CommunicationProtocol.TCP)
        // protocols.add(CommunicationProtocol.UDP)
        // protocols.add(CommunicationProtocol.SERIAL)
        // protocols.add(CommunicationProtocol.MODBUS)
        
        return protocols
    }
    
    override fun isScanningSupported(protocol: CommunicationProtocol): Boolean {
        return getSupportedProtocols().contains(protocol)
    }
}
