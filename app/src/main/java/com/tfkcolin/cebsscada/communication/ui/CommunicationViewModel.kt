package com.tfkcolin.cebsscada.communication.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.cebsscada.communication.CommunicationManager
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.communication.models.CommunicationDevice
import com.tfkcolin.cebsscada.communication.models.CommunicationMessage
import com.tfkcolin.cebsscada.communication.models.MessageResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Unified ViewModel for all communication protocols
 */
@HiltViewModel
class CommunicationViewModel @Inject constructor(
    private val communicationManager: CommunicationManager
) : ViewModel() {
    
    // UI State
    private val _selectedProtocol = MutableStateFlow(CommunicationProtocol.BLUETOOTH_CLASSIC)
    val selectedProtocol: StateFlow<CommunicationProtocol> = _selectedProtocol.asStateFlow()
    
    private val _messages = MutableStateFlow<List<CommunicationMessage>>(emptyList())
    val messages: StateFlow<List<CommunicationMessage>> = _messages.asStateFlow()
    
    private val _isScanning = MutableStateFlow(false)
    val isScanning: StateFlow<Boolean> = _isScanning.asStateFlow()

    // Manual device configuration
    private val _manualDevices = MutableStateFlow<Map<CommunicationProtocol, List<CommunicationDevice>>>(emptyMap())
    val manualDevices: StateFlow<Map<CommunicationProtocol, List<CommunicationDevice>>> = _manualDevices.asStateFlow()
    
    // Derived state flows
    val supportedProtocols = MutableStateFlow(communicationManager.getSupportedProtocols())
    val allDevices = communicationManager.allDevices
    val connectionStates = communicationManager.connectionStates
    val connectedDevices = communicationManager.connectedDevices
    
    // Protocol-specific state
    val currentDevices = combine(allDevices, manualDevices, selectedProtocol) { devices, manual, protocol ->
        val discoveredDevices = devices.filter { it.protocol == protocol }
        val manualDevicesForProtocol = manual[protocol] ?: emptyList()
        discoveredDevices + manualDevicesForProtocol
    }
    
    val currentConnectionState = combine(connectionStates, selectedProtocol) { states, protocol ->
        states[protocol] ?: ConnectionState.DISCONNECTED
    }
    
    val currentConnectedDevice = combine(connectedDevices, selectedProtocol) { devices, protocol ->
        devices[protocol]
    }
    
    init {
        // Collect all messages from communication manager
        viewModelScope.launch {
            communicationManager.allMessages.collect { message ->
                val currentMessages = _messages.value.toMutableList()
                currentMessages.add(message)
                // Keep only last 1000 messages to prevent memory issues
                if (currentMessages.size > 1000) {
                    currentMessages.removeAt(0)
                }
                _messages.value = currentMessages
            }
        }

        // Ensure selected protocol is supported, reset to default if not
        viewModelScope.launch {
            supportedProtocols.collect { protocols ->
                val currentProtocol = _selectedProtocol.value
                if (!protocols.contains(currentProtocol)) {
                    // Reset to first available protocol, preferring Bluetooth Classic
                    val defaultProtocol = protocols.firstOrNull { it == CommunicationProtocol.BLUETOOTH_CLASSIC }
                        ?: protocols.firstOrNull()
                        ?: CommunicationProtocol.BLUETOOTH_CLASSIC
                    _selectedProtocol.value = defaultProtocol
                }
            }
        }
    }
    
    /**
     * Select a communication protocol
     */
    fun selectProtocol(protocol: CommunicationProtocol) {
        _selectedProtocol.value = protocol
    }
    
    /**
     * Start device discovery for the selected protocol
     */
    fun startDiscovery() {
        viewModelScope.launch {
            _isScanning.value = true
            try {
                communicationManager.startDiscovery(_selectedProtocol.value)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    /**
     * Stop device discovery for the selected protocol
     */
    fun stopDiscovery() {
        viewModelScope.launch {
            try {
                communicationManager.stopDiscovery(_selectedProtocol.value)
            } finally {
                _isScanning.value = false
            }
        }
    }
    
    /**
     * Start discovery for all supported protocols
     */
    fun startDiscoveryAll() {
        viewModelScope.launch {
            _isScanning.value = true
            try {
                communicationManager.startDiscoveryAll()
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    /**
     * Stop discovery for all protocols
     */
    fun stopDiscoveryAll() {
        viewModelScope.launch {
            try {
                communicationManager.stopDiscoveryAll()
            } finally {
                _isScanning.value = false
            }
        }
    }
    
    /**
     * Connect to a device
     */
    fun connect(device: CommunicationDevice, options: Map<String, Any> = emptyMap()) {
        viewModelScope.launch {
            communicationManager.connect(device, options)
        }
    }
    
    /**
     * Disconnect from the current device for the selected protocol
     */
    fun disconnect() {
        viewModelScope.launch {
            communicationManager.disconnect(_selectedProtocol.value)
        }
    }
    
    /**
     * Disconnect from all devices
     */
    fun disconnectAll() {
        viewModelScope.launch {
            communicationManager.disconnectAll()
        }
    }
    
    /**
     * Send a text message
     */
    fun sendText(text: String) {
        viewModelScope.launch {
            communicationManager.sendText(_selectedProtocol.value, text, null)
        }
    }
    
    /**
     * Send a message
     */
    fun sendMessage(message: CommunicationMessage) {
        viewModelScope.launch {
            communicationManager.sendMessage(message)
        }
    }
    
    
    
    /**
     * Clear all messages
     */
    fun clearMessages() {
        _messages.value = emptyList()
    }
    
    /**
     * Get messages for a specific protocol
     */
    fun getMessagesForProtocol(protocol: CommunicationProtocol): List<CommunicationMessage> {
        return _messages.value.filter { it.protocol == protocol }
    }
    
    /**
     * Get connection statistics
     */
    fun getConnectionStats() = communicationManager.getAllConnectionStats()
    
    
    /**
     * Check if a protocol supports device scanning
     */
    fun supportsScanning(protocol: CommunicationProtocol): Boolean {
        return when (protocol) {
            CommunicationProtocol.BLUETOOTH_CLASSIC,
            CommunicationProtocol.BLUETOOTH_BLE -> true
            else -> false
        }
    }

    /**
     * Check if a protocol supports manual device configuration
     */
    fun supportsManualConfiguration(protocol: CommunicationProtocol): Boolean {
        return when (protocol) {
            CommunicationProtocol.BLUETOOTH_CLASSIC,
            CommunicationProtocol.BLUETOOTH_BLE -> false // Bluetooth devices are discovered via scanning
            else -> false
        }
    }

    /**
     * Add a manually configured device
     */
    fun addManualDevice(device: CommunicationDevice) {
        val currentManual = _manualDevices.value.toMutableMap()
        val devicesForProtocol = currentManual[device.protocol]?.toMutableList() ?: mutableListOf()

        // Check if device already exists
        if (!devicesForProtocol.any { it.id == device.id }) {
            devicesForProtocol.add(device)
            currentManual[device.protocol] = devicesForProtocol
            _manualDevices.value = currentManual
        }
    }

    /**
     * Remove a manually configured device
     */
    fun removeManualDevice(device: CommunicationDevice) {
        val currentManual = _manualDevices.value.toMutableMap()
        val devicesForProtocol = currentManual[device.protocol]?.toMutableList() ?: return

        devicesForProtocol.removeAll { it.id == device.id }
        if (devicesForProtocol.isEmpty()) {
            currentManual.remove(device.protocol)
        } else {
            currentManual[device.protocol] = devicesForProtocol
        }
        _manualDevices.value = currentManual
    }

    /**
     * Get manually configured devices for a protocol
     */
    fun getManualDevices(protocol: CommunicationProtocol): List<CommunicationDevice> {
        return _manualDevices.value[protocol] ?: emptyList()
    }
    
    override fun onCleared() {
        super.onCleared()
        viewModelScope.launch {
            communicationManager.cleanup()
        }
    }
}
