package com.tfkcolin.cebsscada.communication.base

import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.communication.interfaces.CommunicationService
import com.tfkcolin.cebsscada.communication.models.CommunicationDevice
import com.tfkcolin.cebsscada.communication.models.CommunicationMessage
import com.tfkcolin.cebsscada.communication.models.MessageDirection
import com.tfkcolin.cebsscada.communication.models.MessageResult
import com.tfkcolin.cebsscada.communication.models.TopicSubscription
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * Base implementation of CommunicationService providing common functionality
 */
abstract class BaseCommunicationService : CommunicationService {
    
    // State flows
    private val _connectionState = MutableStateFlow(ConnectionState.DISCONNECTED)
    override val connectionState: StateFlow<ConnectionState> = _connectionState.asStateFlow()
    
    private val _incomingMessages = MutableSharedFlow<CommunicationMessage>()
    override val incomingMessages: Flow<CommunicationMessage> = _incomingMessages.asSharedFlow()
    
    private val _connectionErrors = MutableSharedFlow<String>()
    override val connectionErrors: Flow<String> = _connectionErrors.asSharedFlow()
    
    private val _availableDevices = MutableStateFlow<List<CommunicationDevice>>(emptyList())
    
    private val _subscriptions = MutableStateFlow<List<TopicSubscription>>(emptyList())
    
    // Connection state
    private var _connectedDevice: CommunicationDevice? = null
    override val connectedDevice: CommunicationDevice?
        get() = _connectedDevice
    
    override val isConnected: Boolean
        get() = _connectionState.value == ConnectionState.CONNECTED
    
    private val _isDiscovering = AtomicBoolean(false)
    override val isDiscovering: Boolean
        get() = _isDiscovering.get()
    
    // Statistics
    private val messagesSent = AtomicLong(0)
    private val messagesReceived = AtomicLong(0)
    private val bytesTransferred = AtomicLong(0)
    private val connectionStartTime = AtomicLong(0)
    
    /**
     * Update the connection state
     */
    protected fun updateConnectionState(state: ConnectionState) {
        _connectionState.value = state
        if (state == ConnectionState.CONNECTED) {
            connectionStartTime.set(System.currentTimeMillis())
        } else if (state == ConnectionState.DISCONNECTED) {
            _connectedDevice = null
            connectionStartTime.set(0)
        }
    }
    
    /**
     * Update the connected device
     */
    protected fun updateConnectedDevice(device: CommunicationDevice?) {
        _connectedDevice = device
    }
    
    /**
     * Emit an incoming message
     */
    protected suspend fun emitIncomingMessage(message: CommunicationMessage) {
        messagesReceived.incrementAndGet()
        bytesTransferred.addAndGet(message.size.toLong())
        _incomingMessages.emit(message)
    }
    
    /**
     * Emit a connection error
     */
    protected suspend fun emitConnectionError(error: String) {
        _connectionErrors.emit(error)
    }
    
    /**
     * Update the list of available devices
     */
    protected fun updateAvailableDevices(devices: List<CommunicationDevice>) {
        _availableDevices.value = devices
    }
    
    /**
     * Update discovery state
     */
    protected fun updateDiscoveryState(isDiscovering: Boolean) {
        _isDiscovering.set(isDiscovering)
    }
    
    /**
     * Update subscriptions
     */
    protected fun updateSubscriptions(subscriptions: List<TopicSubscription>) {
        _subscriptions.value = subscriptions
    }
    
    /**
     * Add a subscription
     */
    protected fun addSubscription(subscription: TopicSubscription) {
        val current = _subscriptions.value.toMutableList()
        if (!current.any { it.topic == subscription.topic }) {
            current.add(subscription)
            _subscriptions.value = current
        }
    }
    
    /**
     * Remove a subscription
     */
    protected fun removeSubscription(topic: String) {
        val current = _subscriptions.value.toMutableList()
        current.removeAll { it.topic == topic }
        _subscriptions.value = current
    }
    
    override fun getAvailableDevices(): Flow<List<CommunicationDevice>> {
        return _availableDevices.asStateFlow()
    }
    
    override fun getSubscriptions(): Flow<List<TopicSubscription>> {
        return _subscriptions.asStateFlow()
    }
    
    override suspend fun sendText(text: String, topic: String?): MessageResult {
        val device = connectedDevice ?: return MessageResult(
            success = false,
            messageId = "",
            error = "No device connected"
        )
        
        val message = CommunicationMessage.createTextMessage(
            deviceId = device.id,
            protocol = device.protocol,
            text = text,
            direction = MessageDirection.OUTGOING,
            topic = topic
        )
        
        return sendMessage(message)
    }
    
    override suspend fun sendData(data: ByteArray): MessageResult {
        val device = connectedDevice ?: return MessageResult(
            success = false,
            messageId = "",
            error = "No device connected"
        )
        
        val message = CommunicationMessage.createBinaryMessage(
            deviceId = device.id,
            protocol = device.protocol,
            data = data,
            direction = MessageDirection.OUTGOING
        )
        
        return sendMessage(message)
    }
    
    /**
     * Track sent message statistics
     */
    protected fun trackSentMessage(message: CommunicationMessage) {
        messagesSent.incrementAndGet()
        bytesTransferred.addAndGet(message.size.toLong())
    }
    
    override fun getConnectionStats(): Map<String, Any> {
        val uptime = if (connectionStartTime.get() > 0) {
            System.currentTimeMillis() - connectionStartTime.get()
        } else 0L
        
        return mapOf(
            "messagesSent" to messagesSent.get(),
            "messagesReceived" to messagesReceived.get(),
            "bytesTransferred" to bytesTransferred.get(),
            "connectionUptime" to uptime,
            "isConnected" to isConnected,
            "connectedDevice" to (connectedDevice?.name ?: "None")
        )
    }
    
    // Default implementations for optional features
    override suspend fun subscribe(subscription: TopicSubscription): Boolean = false
    override suspend fun unsubscribe(topic: String): Boolean = false
    override suspend fun startDiscovery() {}
    override suspend fun stopDiscovery() {}
}
