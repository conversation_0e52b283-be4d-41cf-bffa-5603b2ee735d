package com.tfkcolin.cebsscada.communication.interfaces

import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.models.CommunicationDevice
import kotlinx.coroutines.flow.Flow

/**
 * Interface for device discovery/scanning functionality
 */
interface DeviceScanner {
    
    /**
     * The communication protocol this scanner supports
     */
    val supportedProtocol: CommunicationProtocol
    
    /**
     * Flow of discovered devices
     */
    val discoveredDevices: Flow<List<CommunicationDevice>>
    
    /**
     * Flow indicating if scanning is active
     */
    val isScanning: Flow<Boolean>
    
    /**
     * Flow of scanning errors
     */
    val scanningErrors: Flow<String>
    
    /**
     * Start scanning for devices
     * @param options Additional scanning options
     */
    suspend fun startScan(options: Map<String, Any> = emptyMap())
    
    /**
     * Stop scanning for devices
     */
    suspend fun stopScan()
    
    /**
     * Clear the list of discovered devices
     */
    suspend fun clearDevices()
    
    /**
     * Check if scanning is currently active
     */
    val isScanningActive: Boolean
    
    /**
     * Get the current list of discovered devices
     */
    suspend fun getCurrentDevices(): List<CommunicationDevice>
    
    /**
     * Clean up resources
     */
    suspend fun cleanup()
}

/**
 * Interface for communication service factory
 */
interface CommunicationServiceFactory {
    
    /**
     * Create a communication service for the specified protocol
     * @param protocol The communication protocol
     * @return The communication service instance or null if not supported
     */
    fun createService(protocol: CommunicationProtocol): CommunicationService?
    
    /**
     * Get all supported protocols
     * @return List of supported protocols
     */
    fun getSupportedProtocols(): List<CommunicationProtocol>
    
    /**
     * Check if a protocol is supported
     * @param protocol The protocol to check
     * @return true if supported
     */
    fun isProtocolSupported(protocol: CommunicationProtocol): Boolean
}

/**
 * Interface for device scanner factory
 */
interface DeviceScannerFactory {
    
    /**
     * Create a device scanner for the specified protocol
     * @param protocol The communication protocol
     * @return The device scanner instance or null if not supported
     */
    fun createScanner(protocol: CommunicationProtocol): DeviceScanner?
    
    /**
     * Get all supported protocols for scanning
     * @return List of supported protocols
     */
    fun getSupportedProtocols(): List<CommunicationProtocol>
    
    /**
     * Check if a protocol supports scanning
     * @param protocol The protocol to check
     * @return true if scanning is supported
     */
    fun isScanningSupported(protocol: CommunicationProtocol): Boolean
}
