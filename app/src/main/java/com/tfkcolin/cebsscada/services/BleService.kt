package com.tfkcolin.cebsscada.services

import android.bluetooth.*
import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Dedicated service for Bluetooth Low Energy (BLE) communication
 * Optimized for GATT operations and BLE device management
 */
@Singleton
class BleService @Inject constructor(
    private val context: Context,
    private val bluetoothAdapter: BluetoothAdapter?
) {
    companion object {
        private const val TAG = "BleService"
        private const val CONNECTION_TIMEOUT_MS = 10000L
        private const val OPERATION_TIMEOUT_MS = 5000L
    }

    // GATT connection and state
    private var bluetoothGatt: BluetoothGatt? = null
    private var connectedDevice: BluetoothDevice? = null
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    // State flows
    private val _connectionState = MutableStateFlow(BleConnectionState.DISCONNECTED)
    val connectionState: StateFlow<BleConnectionState> = _connectionState.asStateFlow()

    private val _connectedDevice = MutableStateFlow<BluetoothDevice?>(null)
    val connectedDeviceFlow: StateFlow<BluetoothDevice?> = _connectedDevice.asStateFlow()

    private val _gattServices = MutableStateFlow<List<BluetoothGattService>>(emptyList())
    val gattServices: StateFlow<List<BluetoothGattService>> = _gattServices.asStateFlow()

    private val _characteristicData = MutableSharedFlow<BleCharacteristicData>()
    val characteristicData: SharedFlow<BleCharacteristicData> = _characteristicData.asSharedFlow()

    private val _connectionErrors = MutableSharedFlow<String>()
    val connectionErrors: SharedFlow<String> = _connectionErrors.asSharedFlow()

    // Operation tracking
    private val pendingOperations = mutableMapOf<String, CompletableDeferred<Boolean>>()
    private val enabledNotifications = mutableSetOf<String>()

    // Statistics
    private var connectionStartTime = 0L
    private var operationsPerformed = 0L
    private var notificationsReceived = 0L

    /**
     * Connect to a BLE device
     */
    suspend fun connect(device: BluetoothDevice): Boolean = withContext(Dispatchers.Main) {
        if (_connectionState.value == BleConnectionState.CONNECTED) {
            disconnect()
        }

        _connectionState.value = BleConnectionState.CONNECTING
        connectedDevice = device

        try {
            Log.d(TAG, "Connecting to BLE device: ${device.name} (${device.address})")

            val gatt = device.connectGatt(context, false, gattCallback)
            bluetoothGatt = gatt

            // Wait for connection with timeout
            withTimeout(CONNECTION_TIMEOUT_MS) {
                while (_connectionState.value == BleConnectionState.CONNECTING) {
                    delay(100)
                }
            }

            val isConnected = _connectionState.value == BleConnectionState.CONNECTED
            if (isConnected) {
                connectionStartTime = System.currentTimeMillis()
                _connectedDevice.value = device
                Log.d(TAG, "Successfully connected to ${device.name}")
            }

            isConnected

        } catch (e: Exception) {
            Log.e(TAG, "Connection failed: ${e.message}", e)
            _connectionState.value = BleConnectionState.ERROR
            _connectionErrors.emit("Connection failed: ${e.message}")
            cleanup()
            false
        }
    }

    /**
     * Disconnect from the current BLE device
     */
    suspend fun disconnect() = withContext(Dispatchers.Main) {
        _connectionState.value = BleConnectionState.DISCONNECTING

        try {
            // Disable all notifications before disconnecting
            enabledNotifications.clear()
            
            bluetoothGatt?.disconnect()
            bluetoothGatt?.close()
            bluetoothGatt = null
            connectedDevice = null

            _connectionState.value = BleConnectionState.DISCONNECTED
            _connectedDevice.value = null
            _gattServices.value = emptyList()

            Log.d(TAG, "Disconnected from BLE device")
        } catch (e: Exception) {
            Log.w(TAG, "Error during disconnect: ${e.message}")
        }
    }

    /**
     * Read data from a specific characteristic
     */
    suspend fun readCharacteristic(serviceUuid: String, characteristicUuid: String): Boolean {
        val gatt = bluetoothGatt ?: return false
        if (_connectionState.value != BleConnectionState.CONNECTED) return false

        val service = gatt.getService(UUID.fromString(serviceUuid)) ?: return false
        val characteristic = service.getCharacteristic(UUID.fromString(characteristicUuid)) ?: return false

        val operationId = "${serviceUuid}_${characteristicUuid}_read"
        val deferred = CompletableDeferred<Boolean>()
        pendingOperations[operationId] = deferred

        return try {
            val success = gatt.readCharacteristic(characteristic)
            if (success) {
                withTimeout(OPERATION_TIMEOUT_MS) {
                    deferred.await()
                }
            } else {
                pendingOperations.remove(operationId)
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Read characteristic failed: ${e.message}", e)
            pendingOperations.remove(operationId)
            false
        }
    }

    /**
     * Write data to a specific characteristic
     */
    suspend fun writeCharacteristic(
        serviceUuid: String, 
        characteristicUuid: String, 
        data: ByteArray
    ): Boolean {
        val gatt = bluetoothGatt ?: return false
        if (_connectionState.value != BleConnectionState.CONNECTED) return false

        val service = gatt.getService(UUID.fromString(serviceUuid)) ?: return false
        val characteristic = service.getCharacteristic(UUID.fromString(characteristicUuid)) ?: return false

        val operationId = "${serviceUuid}_${characteristicUuid}_write"
        val deferred = CompletableDeferred<Boolean>()
        pendingOperations[operationId] = deferred

        return try {
            characteristic.value = data
            val success = gatt.writeCharacteristic(characteristic)
            
            if (success) {
                withTimeout(OPERATION_TIMEOUT_MS) {
                    deferred.await()
                }
            } else {
                pendingOperations.remove(operationId)
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Write characteristic failed: ${e.message}", e)
            pendingOperations.remove(operationId)
            false
        }
    }

    /**
     * Enable notifications for a characteristic
     */
    suspend fun enableNotifications(serviceUuid: String, characteristicUuid: String): Boolean {
        val gatt = bluetoothGatt ?: return false
        if (_connectionState.value != BleConnectionState.CONNECTED) return false

        val service = gatt.getService(UUID.fromString(serviceUuid)) ?: return false
        val characteristic = service.getCharacteristic(UUID.fromString(characteristicUuid)) ?: return false

        val success = gatt.setCharacteristicNotification(characteristic, true)
        if (success) {
            // Enable notifications on the descriptor
            val descriptor = characteristic.getDescriptor(
                UUID.fromString("00002902-0000-1000-8000-00805f9b34fb")
            )
            descriptor?.let {
                it.value = BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
                gatt.writeDescriptor(it)
                enabledNotifications.add("${serviceUuid}_${characteristicUuid}")
            }
        }

        return success
    }

    /**
     * Disable notifications for a characteristic
     */
    suspend fun disableNotifications(serviceUuid: String, characteristicUuid: String): Boolean {
        val gatt = bluetoothGatt ?: return false
        if (_connectionState.value != BleConnectionState.CONNECTED) return false

        val service = gatt.getService(UUID.fromString(serviceUuid)) ?: return false
        val characteristic = service.getCharacteristic(UUID.fromString(characteristicUuid)) ?: return false

        val success = gatt.setCharacteristicNotification(characteristic, false)
        if (success) {
            // Disable notifications on the descriptor
            val descriptor = characteristic.getDescriptor(
                UUID.fromString("00002902-0000-1000-8000-00805f9b34fb")
            )
            descriptor?.let {
                it.value = BluetoothGattDescriptor.DISABLE_NOTIFICATION_VALUE
                gatt.writeDescriptor(it)
                enabledNotifications.remove("${serviceUuid}_${characteristicUuid}")
            }
        }

        return success
    }

    /**
     * Get connection statistics
     */
    fun getConnectionStats(): BleStats {
        val uptime = if (connectionStartTime > 0) {
            System.currentTimeMillis() - connectionStartTime
        } else 0L

        return BleStats(
            isConnected = _connectionState.value == BleConnectionState.CONNECTED,
            connectedDeviceName = connectedDevice?.name ?: "None",
            connectedDeviceAddress = connectedDevice?.address ?: "None",
            connectionUptime = uptime,
            servicesDiscovered = _gattServices.value.size,
            operationsPerformed = operationsPerformed,
            notificationsReceived = notificationsReceived,
            enabledNotifications = enabledNotifications.size
        )
    }

    /**
     * GATT callback for handling BLE events
     */
    private val gattCallback = object : BluetoothGattCallback() {
        override fun onConnectionStateChange(gatt: BluetoothGatt?, status: Int, newState: Int) {
            super.onConnectionStateChange(gatt, status, newState)
            
            when (newState) {
                BluetoothProfile.STATE_CONNECTED -> {
                    Log.d(TAG, "GATT connected, discovering services...")
                    gatt?.discoverServices()
                }
                BluetoothProfile.STATE_DISCONNECTED -> {
                    Log.d(TAG, "GATT disconnected")
                    _connectionState.value = BleConnectionState.DISCONNECTED
                    cleanup()
                }
            }
        }

        override fun onServicesDiscovered(gatt: BluetoothGatt?, status: Int) {
            super.onServicesDiscovered(gatt, status)
            
            if (status == BluetoothGatt.GATT_SUCCESS) {
                val services = gatt?.services ?: emptyList()
                _gattServices.value = services
                _connectionState.value = BleConnectionState.CONNECTED
                
                Log.d(TAG, "Services discovered: ${services.size} services")
                services.forEach { service ->
                    Log.d(TAG, "Service: ${service.uuid}")
                    service.characteristics.forEach { characteristic ->
                        Log.d(TAG, "  Characteristic: ${characteristic.uuid}")
                    }
                }
            } else {
                Log.e(TAG, "Service discovery failed with status: $status")
                _connectionState.value = BleConnectionState.ERROR
                serviceScope.launch {
                    _connectionErrors.emit("Service discovery failed")
                }
            }
        }

        override fun onCharacteristicRead(
            gatt: BluetoothGatt?,
            characteristic: BluetoothGattCharacteristic?,
            status: Int
        ) {
            super.onCharacteristicRead(gatt, characteristic, status)
            
            characteristic?.let { char ->
                val serviceUuid = char.service.uuid.toString()
                val characteristicUuid = char.uuid.toString()
                val operationId = "${serviceUuid}_${characteristicUuid}_read"
                
                val deferred = pendingOperations.remove(operationId)
                
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    operationsPerformed++
                    
                    val data = BleCharacteristicData(
                        serviceUuid = serviceUuid,
                        characteristicUuid = characteristicUuid,
                        data = char.value ?: byteArrayOf(),
                        timestamp = System.currentTimeMillis(),
                        operation = BleOperation.READ
                    )
                    
                    serviceScope.launch {
                        _characteristicData.emit(data)
                    }
                    
                    deferred?.complete(true)
                } else {
                    Log.e(TAG, "Characteristic read failed with status: $status")
                    deferred?.complete(false)
                }
            }
        }

        override fun onCharacteristicWrite(
            gatt: BluetoothGatt?,
            characteristic: BluetoothGattCharacteristic?,
            status: Int
        ) {
            super.onCharacteristicWrite(gatt, characteristic, status)
            
            characteristic?.let { char ->
                val serviceUuid = char.service.uuid.toString()
                val characteristicUuid = char.uuid.toString()
                val operationId = "${serviceUuid}_${characteristicUuid}_write"
                
                val deferred = pendingOperations.remove(operationId)
                
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    operationsPerformed++
                    deferred?.complete(true)
                } else {
                    Log.e(TAG, "Characteristic write failed with status: $status")
                    deferred?.complete(false)
                }
            }
        }

        override fun onCharacteristicChanged(
            gatt: BluetoothGatt?,
            characteristic: BluetoothGattCharacteristic?
        ) {
            super.onCharacteristicChanged(gatt, characteristic)
            
            characteristic?.let { char ->
                notificationsReceived++
                
                val data = BleCharacteristicData(
                    serviceUuid = char.service.uuid.toString(),
                    characteristicUuid = char.uuid.toString(),
                    data = char.value ?: byteArrayOf(),
                    timestamp = System.currentTimeMillis(),
                    operation = BleOperation.NOTIFICATION
                )
                
                serviceScope.launch {
                    _characteristicData.emit(data)
                }
            }
        }
    }

    /**
     * Clean up resources
     */
    private fun cleanup() {
        pendingOperations.values.forEach { it.complete(false) }
        pendingOperations.clear()
        enabledNotifications.clear()
    }

    /**
     * Clean up service resources
     */
    fun cleanupService() {
        serviceScope.cancel()
        runBlocking {
            disconnect()
        }
    }
}

/**
 * Connection states for BLE
 */
enum class BleConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    DISCONNECTING,
    ERROR
}

/**
 * BLE operations
 */
enum class BleOperation {
    READ,
    WRITE,
    NOTIFICATION
}

/**
 * Data from BLE characteristic operations
 */
data class BleCharacteristicData(
    val serviceUuid: String,
    val characteristicUuid: String,
    val data: ByteArray,
    val timestamp: Long,
    val operation: BleOperation
) {
    val text: String get() = String(data)
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        other as BleCharacteristicData
        return serviceUuid == other.serviceUuid &&
               characteristicUuid == other.characteristicUuid &&
               data.contentEquals(other.data) &&
               timestamp == other.timestamp &&
               operation == other.operation
    }

    override fun hashCode(): Int {
        var result = serviceUuid.hashCode()
        result = 31 * result + characteristicUuid.hashCode()
        result = 31 * result + data.contentHashCode()
        result = 31 * result + timestamp.hashCode()
        result = 31 * result + operation.hashCode()
        return result
    }
}

/**
 * Connection statistics for BLE
 */
data class BleStats(
    val isConnected: Boolean,
    val connectedDeviceName: String,
    val connectedDeviceAddress: String,
    val connectionUptime: Long,
    val servicesDiscovered: Int,
    val operationsPerformed: Long,
    val notificationsReceived: Long,
    val enabledNotifications: Int
)
