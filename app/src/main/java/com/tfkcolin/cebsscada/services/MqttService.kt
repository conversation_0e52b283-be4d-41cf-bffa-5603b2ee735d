package com.tfkcolin.cebsscada.services

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import org.eclipse.paho.client.mqttv3.*
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Dedicated service for MQTT communication
 * Optimized for publish-subscribe messaging and broker management
 */
@Singleton
class MqttService @Inject constructor(
    private val context: Context
) : MqttCallback {
    companion object {
        private const val TAG = "MqttService"
        private const val CONNECTION_TIMEOUT = 30
        private const val KEEP_ALIVE_INTERVAL = 60
        private const val MAX_RECONNECT_ATTEMPTS = 5
        private const val RECONNECT_DELAY_MS = 5000L
    }

    // MQTT client and connection state
    private var mqttClient: MqttAsyncClient? = null
    private var currentBroker: MqttBrokerConfig? = null
    private var isAutoReconnectEnabled = true
    private var reconnectAttempts = 0

    // Coroutine scope for service operations
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // State flows
    private val _connectionState = MutableStateFlow(MqttConnectionState.DISCONNECTED)
    val connectionState: StateFlow<MqttConnectionState> = _connectionState.asStateFlow()

    private val _connectedBroker = MutableStateFlow<MqttBrokerConfig?>(null)
    val connectedBroker: StateFlow<MqttBrokerConfig?> = _connectedBroker.asStateFlow()

    private val _subscriptions = MutableStateFlow<List<MqttSubscription>>(emptyList())
    val subscriptions: StateFlow<List<MqttSubscription>> = _subscriptions.asStateFlow()

    private val _receivedMessages = MutableSharedFlow<MqttReceivedMessage>()
    val receivedMessages: SharedFlow<MqttReceivedMessage> = _receivedMessages.asSharedFlow()

    private val _connectionErrors = MutableSharedFlow<String>()
    val connectionErrors: SharedFlow<String> = _connectionErrors.asSharedFlow()

    // Statistics
    private var connectionStartTime = 0L
    private var messagesSent = 0L
    private var messagesReceived = 0L
    private var bytesTransferred = 0L

    /**
     * Connect to an MQTT broker
     */
    suspend fun connect(brokerConfig: MqttBrokerConfig): Boolean = withContext(Dispatchers.IO) {
        if (_connectionState.value == MqttConnectionState.CONNECTED) {
            disconnect()
        }

        _connectionState.value = MqttConnectionState.CONNECTING
        currentBroker = brokerConfig
        reconnectAttempts = 0

        try {
            Log.d(TAG, "Connecting to MQTT broker: ${brokerConfig.address}:${brokerConfig.port}")

            val brokerUrl = if (brokerConfig.useSSL) {
                "ssl://${brokerConfig.address}:${brokerConfig.port}"
            } else {
                "tcp://${brokerConfig.address}:${brokerConfig.port}"
            }

            val clientId = brokerConfig.clientId ?: "android_mqtt_client_${System.currentTimeMillis()}"
            
            mqttClient = MqttAsyncClient(brokerUrl, clientId, MemoryPersistence())
            mqttClient?.setCallback(this@MqttService)

            val connectOptions = MqttConnectOptions().apply {
                isCleanSession = brokerConfig.cleanSession
                connectionTimeout = CONNECTION_TIMEOUT
                keepAliveInterval = KEEP_ALIVE_INTERVAL
                isAutomaticReconnect = false // We handle reconnection manually

                brokerConfig.username?.let { userName = it }
                brokerConfig.password?.let { password = it.toCharArray() }

                if (brokerConfig.lastWillTopic != null && brokerConfig.lastWillMessage != null) {
                    setWill(
                        brokerConfig.lastWillTopic,
                        brokerConfig.lastWillMessage.toByteArray(),
                        brokerConfig.lastWillQos,
                        brokerConfig.lastWillRetain
                    )
                }
            }

            val token = mqttClient?.connect(connectOptions)
            token?.waitForCompletion(CONNECTION_TIMEOUT * 1000L)

            if (token?.isComplete == true && mqttClient?.isConnected == true) {
                _connectionState.value = MqttConnectionState.CONNECTED
                _connectedBroker.value = brokerConfig
                connectionStartTime = System.currentTimeMillis()
                
                Log.d(TAG, "Successfully connected to MQTT broker")
                true
            } else {
                throw Exception("Connection failed or timed out")
            }

        } catch (e: Exception) {
            Log.e(TAG, "MQTT connection failed: ${e.message}", e)
            _connectionState.value = MqttConnectionState.ERROR
            _connectionErrors.emit("Connection failed: ${e.message}")
            
            if (isAutoReconnectEnabled && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                scheduleReconnect()
            }
            false
        }
    }

    /**
     * Disconnect from the MQTT broker
     */
    suspend fun disconnect() = withContext(Dispatchers.IO) {
        _connectionState.value = MqttConnectionState.DISCONNECTING

        try {
            mqttClient?.disconnect()?.waitForCompletion(5000)
            mqttClient?.close()
        } catch (e: Exception) {
            Log.w(TAG, "Error during disconnect: ${e.message}")
        }

        mqttClient = null
        currentBroker = null
        _connectionState.value = MqttConnectionState.DISCONNECTED
        _connectedBroker.value = null
        _subscriptions.value = emptyList()

        Log.d(TAG, "Disconnected from MQTT broker")
    }

    /**
     * Publish a message to a topic
     */
    suspend fun publish(
        topic: String,
        message: String,
        qos: Int = 0,
        retained: Boolean = false
    ): Boolean = withContext(Dispatchers.IO) {
        val client = mqttClient
        if (client == null || !client.isConnected) {
            _connectionErrors.emit("Not connected to broker")
            return@withContext false
        }

        try {
            val mqttMessage = MqttMessage(message.toByteArray()).apply {
                this.qos = qos
                isRetained = retained
            }

            val token = client.publish(topic, mqttMessage)
            token.waitForCompletion(5000)

            if (token.isComplete) {
                messagesSent++
                bytesTransferred += message.toByteArray().size
                Log.d(TAG, "Published message to topic '$topic': $message")
                true
            } else {
                false
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to publish message: ${e.message}", e)
            _connectionErrors.emit("Publish failed: ${e.message}")
            false
        }
    }

    /**
     * Subscribe to a topic
     */
    suspend fun subscribe(topic: String, qos: Int = 0): Boolean = withContext(Dispatchers.IO) {
        val client = mqttClient
        if (client == null || !client.isConnected) {
            _connectionErrors.emit("Not connected to broker")
            return@withContext false
        }

        try {
            val token = client.subscribe(topic, qos)
            token.waitForCompletion(5000)

            if (token.isComplete) {
                val currentSubs = _subscriptions.value.toMutableList()
                val existingIndex = currentSubs.indexOfFirst { it.topic == topic }
                
                val subscription = MqttSubscription(
                    topic = topic,
                    qos = qos,
                    subscribeTime = System.currentTimeMillis()
                )

                if (existingIndex >= 0) {
                    currentSubs[existingIndex] = subscription
                } else {
                    currentSubs.add(subscription)
                }

                _subscriptions.value = currentSubs
                Log.d(TAG, "Subscribed to topic '$topic' with QoS $qos")
                true
            } else {
                false
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to subscribe to topic '$topic': ${e.message}", e)
            _connectionErrors.emit("Subscribe failed: ${e.message}")
            false
        }
    }

    /**
     * Unsubscribe from a topic
     */
    suspend fun unsubscribe(topic: String): Boolean = withContext(Dispatchers.IO) {
        val client = mqttClient
        if (client == null || !client.isConnected) {
            _connectionErrors.emit("Not connected to broker")
            return@withContext false
        }

        try {
            val token = client.unsubscribe(topic)
            token.waitForCompletion(5000)

            if (token.isComplete) {
                val currentSubs = _subscriptions.value.toMutableList()
                currentSubs.removeAll { it.topic == topic }
                _subscriptions.value = currentSubs

                Log.d(TAG, "Unsubscribed from topic '$topic'")
                true
            } else {
                false
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to unsubscribe from topic '$topic': ${e.message}", e)
            _connectionErrors.emit("Unsubscribe failed: ${e.message}")
            false
        }
    }

    /**
     * Get connection statistics
     */
    fun getConnectionStats(): MqttStats {
        val uptime = if (connectionStartTime > 0) {
            System.currentTimeMillis() - connectionStartTime
        } else 0L

        return MqttStats(
            isConnected = _connectionState.value == MqttConnectionState.CONNECTED,
            brokerAddress = currentBroker?.address ?: "None",
            brokerPort = currentBroker?.port ?: 0,
            clientId = mqttClient?.clientId ?: "None",
            connectionUptime = uptime,
            messagesSent = messagesSent,
            messagesReceived = messagesReceived,
            bytesTransferred = bytesTransferred,
            activeSubscriptions = _subscriptions.value.size
        )
    }

    /**
     * Enable or disable auto-reconnect
     */
    fun setAutoReconnectEnabled(enabled: Boolean) {
        isAutoReconnectEnabled = enabled
        Log.d(TAG, "Auto-reconnect ${if (enabled) "enabled" else "disabled"}")
    }

    // MqttCallback implementation
    override fun connectionLost(cause: Throwable?) {
        Log.w(TAG, "MQTT connection lost: ${cause?.message}")
        _connectionState.value = MqttConnectionState.ERROR
        
        serviceScope.launch {
            _connectionErrors.emit("Connection lost: ${cause?.message}")
            
            if (isAutoReconnectEnabled && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                scheduleReconnect()
            }
        }
    }

    override fun messageArrived(topic: String?, message: MqttMessage?) {
        if (topic != null && message != null) {
            messagesReceived++
            bytesTransferred += message.payload.size

            val receivedMessage = MqttReceivedMessage(
                topic = topic,
                message = String(message.payload),
                qos = message.qos,
                retained = message.isRetained,
                timestamp = System.currentTimeMillis()
            )

            serviceScope.launch {
                _receivedMessages.emit(receivedMessage)
            }

            Log.d(TAG, "Message received on topic '$topic': ${String(message.payload)}")
        }
    }

    override fun deliveryComplete(token: IMqttDeliveryToken?) {
        Log.d(TAG, "Message delivery complete")
    }

    /**
     * Schedule reconnection attempt
     */
    private fun scheduleReconnect() {
        if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
            Log.w(TAG, "Max reconnect attempts reached")
            return
        }

        serviceScope.launch {
            reconnectAttempts++
            _connectionState.value = MqttConnectionState.RECONNECTING
            
            Log.d(TAG, "Scheduling reconnect attempt $reconnectAttempts in ${RECONNECT_DELAY_MS}ms")
            delay(RECONNECT_DELAY_MS)
            
            currentBroker?.let { broker ->
                if (_connectionState.value == MqttConnectionState.RECONNECTING) {
                    connect(broker)
                }
            }
        }
    }

    /**
     * Clean up resources
     */
    fun cleanup() {
        serviceScope.cancel()
        runBlocking {
            disconnect()
        }
    }
}

/**
 * Connection states for MQTT
 */
enum class MqttConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    DISCONNECTING,
    ERROR,
    RECONNECTING
}

/**
 * MQTT broker configuration
 */
data class MqttBrokerConfig(
    val address: String,
    val port: Int,
    val clientId: String? = null,
    val username: String? = null,
    val password: String? = null,
    val useSSL: Boolean = false,
    val cleanSession: Boolean = true,
    val lastWillTopic: String? = null,
    val lastWillMessage: String? = null,
    val lastWillQos: Int = 0,
    val lastWillRetain: Boolean = false
)

/**
 * MQTT subscription information
 */
data class MqttSubscription(
    val topic: String,
    val qos: Int,
    val subscribeTime: Long
)

/**
 * Received MQTT message
 */
data class MqttReceivedMessage(
    val topic: String,
    val message: String,
    val qos: Int,
    val retained: Boolean,
    val timestamp: Long
)

/**
 * Connection statistics for MQTT
 */
data class MqttStats(
    val isConnected: Boolean,
    val brokerAddress: String,
    val brokerPort: Int,
    val clientId: String,
    val connectionUptime: Long,
    val messagesSent: Long,
    val messagesReceived: Long,
    val bytesTransferred: Long,
    val activeSubscriptions: Int
)
